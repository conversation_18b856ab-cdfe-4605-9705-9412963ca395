# Vercel Blob Storage Migration Guide

## Overview

This guide documents the migration from local image storage to Vercel Blob Storage for the Laravel backend and React frontend application.

## Changes Made

### Backend (Laravel) Changes

#### 1. Dependencies Added

- Added `guzzlehttp/guzzle` to `composer.json` for HTTP requests to Vercel Blob API

#### 2. New Services Created

- **`app/Services/VercelBlobService.php`**: Core service for Vercel Blob operations
  - `uploadFile()`: Upload files to Vercel Blob Storage
  - `deleteFile()`: Delete files from Vercel Blob Storage
  - `generateFilename()`: Generate unique filenames
  - `isConfigured()`: Check if Vercel Blob is properly configured

#### 3. Updated Services

- **`app/Services/ImageService.php`**: Modified to use Vercel Blob Storage
  - Updated `uploadImage()` to use Vercel Blob instead of local storage
  - Modified `createThumbnail()` to upload thumbnails to Vercel Blob
  - Updated `deleteImage()` to delete from Vercel Blob
  - Simplified `moveImagesToProduct()` since cloud files don't need moving

#### 4. Configuration Updates

- **`config/services.php`**: Added Vercel Blob configuration
- **`.env.example`**: Added Vercel Blob environment variables

#### 5. Service Provider

- **`app/Providers/VercelBlobServiceProvider.php`**: Registers VercelBlobService
- **`bootstrap/app.php`**: Registered the new service provider

#### 6. Model Updates

- **`app/Models/Product.php`**: Updated `getImagePathAttribute()` to handle Vercel URLs
- **`app/Http/Resources/ProductImageResource.php`**: Updated to return Vercel Blob URLs

### Frontend (React/Next.js) Changes

#### 1. Next.js Configuration

- **`next.config.js`**: Added Vercel Blob domain to `remotePatterns` for Next.js Image component

## Environment Variables Required

Add these to your `.env` file:

```env
VERCEL_BLOB_BASE_URL=https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com
VERCEL_BLOB_TOKEN=your_vercel_blob_token_here
VERCEL_BLOB_STORE_ID=store_cjtsSc4w6vLftQkJ
```

## Installation Steps

### 1. Install Dependencies

```bash
cd azstock-laravel
composer install
```

### 2. Update Environment Variables

Copy the Vercel Blob credentials to your `.env` file:

```env
VERCEL_BLOB_TOKEN=your_actual_token_here
```

### 3. Clear Configuration Cache

```bash
php artisan config:clear
php artisan cache:clear
```

## API Endpoints

The following API endpoints will now use Vercel Blob Storage:

- `POST /api/images/upload` - Upload images to temporary storage
- `POST /api/images/products` - Upload product images
- `DELETE /api/images/{image}` - Delete product images

## Database Schema

The existing database schema supports Vercel Blob URLs:

- `products.image` - Can store full Vercel Blob URLs
- `product_images.image_path` - Can store full Vercel Blob URLs
- `images.file_path` - Can store full Vercel Blob URLs

## Backward Compatibility

The implementation maintains backward compatibility:

- Existing local storage URLs will continue to work
- New uploads will use Vercel Blob Storage
- The system can handle both URL formats simultaneously

## Testing

### 1. Test Image Upload

```bash
# Test the upload endpoint
curl -X POST http://localhost:8000/api/images/upload \
  -H "Authorization: Bearer your_token" \
  -F "images[0]=@test-image.jpg"
```

### 2. Verify Frontend

- Check that images display correctly in the frontend
- Test image upload functionality in the dashboard
- Verify thumbnail generation works

## Troubleshooting

### Common Issues

1. **"Failed to upload file to Vercel Blob"**

   - Check that `VERCEL_BLOB_TOKEN` is set correctly
   - Verify the token has upload permissions

2. **Images not displaying**

   - Ensure Vercel Blob domain is added to Next.js `remotePatterns`
   - Check that URLs are being generated correctly

3. **Thumbnail creation fails**
   - Check that Intervention Image is installed
   - Verify temporary directory permissions

### Debug Commands

```bash
# Test Vercel Blob configuration
php artisan vercel:test-blob

# Check configuration
php artisan config:show services.vercel_blob

# Test Vercel Blob service
php artisan tinker
>>> app(App\Services\VercelBlobService::class)->isConfigured()
```

## Artisan Commands

### 1. Test Vercel Blob Configuration

```bash
php artisan vercel:test-blob
```

This command verifies that Vercel Blob Storage is properly configured.

### 2. Migrate Existing Images

```bash
# Dry run to see what would be migrated
php artisan vercel:migrate-images --dry-run

# Actually migrate the images
php artisan vercel:migrate-images
```

This command migrates existing local images to Vercel Blob Storage.

## Migration Strategy

### For Existing Images

1. **Gradual Migration**: New uploads use Vercel Blob, existing images remain local
2. **Batch Migration**: Create a command to migrate existing images to Vercel Blob
3. **Hybrid Approach**: Keep both systems running simultaneously

### Recommended Approach

Start with gradual migration:

1. Deploy the new code
2. New uploads will use Vercel Blob
3. Existing images continue to work from local storage
4. Optionally migrate existing images later

## Security Considerations

- Vercel Blob URLs are publicly accessible
- Consider implementing signed URLs for sensitive content
- Validate file types and sizes before upload
- Monitor storage usage and costs

## Performance Notes

- Vercel Blob provides global CDN distribution
- Faster image loading for users worldwide
- Reduced server storage requirements
- Automatic image optimization available

## Next Steps

1. Install dependencies with `composer install`
2. Set up Vercel Blob credentials
3. Test image upload functionality
4. Deploy to production
5. Monitor for any issues
6. Consider migrating existing images if needed
