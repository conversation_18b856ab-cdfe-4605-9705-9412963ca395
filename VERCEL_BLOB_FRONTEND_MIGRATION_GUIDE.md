# Vercel Blob Storage Migration Guide (Frontend-First Approach)

## Overview

This guide documents the migration from local image storage to Vercel Blob Storage using a **frontend-first approach**. Images are uploaded directly from the React frontend to Vercel Blob Storage, then the URLs are sent to the Laravel backend for database storage.

## Architecture

### Frontend-First Approach

1. **Frontend**: Uploads images directly to Vercel Blob Storage using `@vercel/blob`
2. **Frontend**: Sends Vercel Blob URLs to Laravel backend
3. **Backend**: Stores URLs in database and serves them via API
4. **Frontend**: Displays images using Vercel Blob URLs

This approach is more efficient than proxying uploads through the backend and follows Vercel's recommended practices.

## Changes Made

### Frontend (React/Next.js) Changes

#### 1. New Services Created

- **`src/services/VercelBlobService.ts`**: Core service for Vercel Blob operations
  - `uploadFile()`: Upload single file to Vercel Blob Storage
  - `uploadFiles()`: Upload multiple files to Vercel Blob Storage
  - `deleteFile()`: Delete files from Vercel Blob Storage
  - `generateFilename()`: Generate unique filenames

#### 2. Updated Services

- **`src/modules/dashboard/services/VendorService.ts`**: Modified to use Vercel Blob
  - `uploadImages()`: Now uploads directly to Vercel Blob, then sends URLs to backend
  - `createProduct()`: Uses Vercel Blob for image uploads
  - `updateProduct()`: Uses Vercel Blob for image uploads

#### 3. Next.js Configuration

- **`next.config.js`**: Added Vercel Blob domain to `remotePatterns` for Next.js Image component

### Backend (Laravel) Changes

#### 1. Updated Controllers

- **`app/Http/Controllers/Api/ImageController.php`**: Modified to accept Vercel Blob URLs instead of file uploads
- **`app/Http/Controllers/ProductController.php`**: Updated to handle both file uploads and Vercel Blob URLs

#### 2. Model Updates

- **`app/Models/Product.php`**: Updated `getImagePathAttribute()` to handle Vercel URLs
- **`app/Http/Resources/ProductImageResource.php`**: Updated to return correct URLs for both local and Vercel Blob images

## Environment Variables Required

### Frontend (.env)

```env
NEXT_PUBLIC_BLOB_READ_WRITE_TOKEN=your_vercel_blob_token_here
```

### Backend (.env)

No additional environment variables needed - the backend only stores and serves URLs.

## Installation Steps

### 1. Frontend Setup

The frontend already has the required dependencies:

- `@vercel/blob` package is installed
- `BLOB_READ_WRITE_TOKEN` is configured

### 2. Backend Setup

No additional dependencies or configuration needed.

## How It Works

### Image Upload Flow

1. User selects images in the frontend
2. Frontend uploads images directly to Vercel Blob Storage
3. Frontend receives Vercel Blob URLs
4. Frontend sends URLs to Laravel backend via API
5. Backend validates URLs and stores them in database
6. Backend returns success response

### Image Display Flow

1. Frontend requests product data from Laravel API
2. Backend returns product data with Vercel Blob URLs
3. Frontend displays images using Vercel Blob URLs
4. Images are served directly from Vercel's global CDN

## API Changes

### Upload Endpoint

**Before**: `POST /api/images/upload` (with file uploads)

```javascript
// Old approach - file uploads
const formData = new FormData();
formData.append("images[0]", file);
```

**After**: `POST /api/images/upload` (with URLs)

```javascript
// New approach - Vercel Blob URLs
const payload = {
  images: [
    {
      url: "https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com/...",
      original_name: "image.jpg",
      size: 1024000,
    },
  ],
};
```

## Benefits

### Performance

- **Faster uploads**: Direct to Vercel Blob, no backend bottleneck
- **Global CDN**: Images served from edge locations worldwide
- **Reduced server load**: No file processing on Laravel server

### Scalability

- **No storage limits**: Vercel Blob handles storage scaling
- **No server disk usage**: Images stored in cloud
- **Better bandwidth**: Vercel's global infrastructure

### Developer Experience

- **Simpler backend**: No file handling logic needed
- **Better error handling**: Upload errors handled on frontend
- **Easier debugging**: Clear separation of concerns

## Testing

### 1. Test Image Upload

1. Go to the vendor dashboard
2. Create or edit a product
3. Upload images using the image upload component
4. Verify images are uploaded to Vercel Blob Storage
5. Check that URLs are saved in the database

### 2. Test Image Display

1. View products in the frontend
2. Verify images load correctly
3. Check browser network tab to confirm images are served from Vercel Blob

## Troubleshooting

### Common Issues

1. **"Vercel Blob Storage is not configured"**

   - Check that `NEXT_PUBLIC_BLOB_READ_WRITE_TOKEN` is set in frontend `.env`
   - Verify the token has read/write permissions
   - Restart the Next.js development server after changing environment variables

2. **Images not uploading**

   - Check browser console for JavaScript errors
   - Verify Vercel Blob token is valid
   - Check network tab for failed requests

3. **Images not displaying**
   - Ensure Vercel Blob domain is in Next.js `remotePatterns`
   - Check that URLs in database are valid
   - Verify images exist in Vercel Blob Storage

### Debug Commands

```bash
# Check if Vercel Blob service is configured (frontend)
console.log(vercelBlobService.isConfigured());

# Check stored URLs in database (backend)
php artisan tinker
>>> App\Models\Product::first()->image
>>> App\Models\ProductImage::first()->image_path
```

## Migration Strategy

### Recommended Approach: Gradual Migration

1. Deploy the new code
2. New uploads automatically use Vercel Blob Storage
3. Existing local images continue to work
4. Optionally migrate existing images later

### Backward Compatibility

The implementation maintains full backward compatibility:

- Existing local storage URLs continue to work
- New uploads use Vercel Blob Storage
- The system handles both URL formats seamlessly

## Security Considerations

- Vercel Blob URLs are publicly accessible
- File validation happens on the frontend before upload
- Backend validates that URLs are from the correct Vercel Blob store
- Consider implementing signed URLs for sensitive content

## Next Steps

1. Test the image upload functionality
2. Monitor upload performance and error rates
3. Consider implementing thumbnail generation
4. Optionally migrate existing local images to Vercel Blob
5. Set up monitoring for Vercel Blob usage and costs
