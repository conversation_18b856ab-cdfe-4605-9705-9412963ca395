{"name": "project-base", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@supabase/ssr": "^0.5.2", "@tanstack/react-query": "^5.79.2", "@types/aos": "^3.0.7", "aos": "^2.3.4", "date-fns": "^4.1.0", "embla-carousel": "^8.0.0-rc19", "embla-carousel-react": "^8.0.0-rc19", "embla-carousel-wheel-gestures": "^8.0.0-rc05", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "next": "^14.2.3", "next-auth": "^4.24.11", "next-http-proxy-middleware": "^1.2.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.47.0", "react-icons": "^5.0.1", "react-select": "^5.9.0", "zod": "^3.22.4"}, "devDependencies": {"@types/lodash": "^4.17.13", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10", "daisyui": "^4.12.24", "eslint": "^8", "eslint-config-next": "^14.2.3", "postcss": "^8", "tailwindcss": "^3", "typescript": "^5"}}