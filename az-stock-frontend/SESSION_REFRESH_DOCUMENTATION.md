# Session Refresh System Documentation

## Overview

The Session Refresh System provides automatic and manual token refresh functionality for the Next.js frontend application. It ensures that user session data (roles, verification status, profile information) stays up-to-date without requiring users to log out and back in.

## Key Features

- **Automatic Token Refresh**: Periodically refreshes session data in the background
- **Manual Refresh**: Allows users to manually trigger session updates
- **Event-Driven Updates**: Triggers session refresh when specific events occur
- **Silent Refresh**: Updates session data without user interface feedback
- **Force Refresh**: Bypasses cooldowns and concurrent request checks
- **Real-time Updates**: Immediately reflects changes in verification status and roles

## Architecture

### Components

1. **Enhanced NextAuth Configuration** (`src/utils/auth.ts`)
   - Modified JWT callback to support session updates
   - Auto-refresh logic for tokens older than 5 minutes
   - <PERSON>les session update triggers

2. **Session Refresh Hook** (`src/hooks/useSessionRefresh.ts`)
   - React hook for session refresh functionality
   - Provides methods for different types of refresh operations

3. **Session Service** (`src/services/SessionService.ts`)
   - Singleton service for managing session operations
   - Handles API calls to fetch updated user data
   - Manages refresh cooldowns and concurrent requests

4. **Session Provider** (`src/components/SessionProvider.tsx`)
   - React context provider for session management
   - Auto-refresh functionality with configurable intervals
   - Visibility change detection for smart refreshing

5. **Session Utils** (`src/utils/sessionUtils.ts`)
   - Utility functions for triggering specific update events
   - Event listeners setup and management
   - Helper functions for common session operations

## Usage

### Basic Setup

1. **Wrap your app with the Session Provider**:

```tsx
import { SessionProvider as CustomSessionProvider } from "@/components/SessionProvider";

function App({ children }) {
  return (
    <NextAuthSessionProvider>
      <CustomSessionProvider
        autoRefreshInterval={5 * 60 * 1000} // 5 minutes
        enableAutoRefresh={true}
      >
        {children}
      </CustomSessionProvider>
    </NextAuthSessionProvider>
  );
}
```

2. **Use the session refresh hook in components**:

```tsx
import useSessionRefresh from "@/hooks/useSessionRefresh";

function MyComponent() {
  const { refreshSession, refreshSessionSilently, isRefreshing } = useSessionRefresh();

  const handleRefresh = async () => {
    await refreshSession();
  };

  return (
    <button onClick={handleRefresh} disabled={isRefreshing}>
      {isRefreshing ? "Refreshing..." : "Refresh Session"}
    </button>
  );
}
```

### Triggering Updates After API Calls

#### After User Verification

```tsx
import { triggerVerificationUpdate } from "@/utils/sessionUtils";

const handleVerificationSubmit = async (data) => {
  try {
    await api.post('/admin/users/123/verify', data);
    // Trigger session refresh to get updated verification status
    await triggerVerificationUpdate();
  } catch (error) {
    console.error("Verification failed:", error);
  }
};
```

#### After Role Changes

```tsx
import { triggerRoleUpdate } from "@/utils/sessionUtils";

const handleRoleChange = async (userId, newRoles) => {
  try {
    await api.put(`/admin/users/${userId}`, { roles: newRoles });
    // Trigger session refresh to get updated roles
    await triggerRoleUpdate();
  } catch (error) {
    console.error("Role update failed:", error);
  }
};
```

#### After Profile Updates

```tsx
import { triggerProfileUpdate } from "@/utils/sessionUtils";

const handleProfileUpdate = async (profileData) => {
  try {
    await api.put('/user/profile', profileData);
    // Trigger session refresh to get updated profile data
    await triggerProfileUpdate();
  } catch (error) {
    console.error("Profile update failed:", error);
  }
};
```

### Event Listeners

Set up event listeners to respond to session updates:

```tsx
import { setupSessionEventListeners } from "@/utils/sessionUtils";

useEffect(() => {
  const cleanup = setupSessionEventListeners({
    onVerificationUpdate: () => {
      console.log("Verification status updated!");
      // Update UI or show notification
    },
    onRoleUpdate: () => {
      console.log("User roles updated!");
      // Redirect or update navigation
    },
    onProfileUpdate: () => {
      console.log("Profile updated!");
      // Refresh profile display
    },
  });

  return cleanup; // Clean up event listeners
}, []);
```

## API Integration

The system integrates with the Laravel backend's `/api/user` endpoint to fetch updated user data. The endpoint should return:

```json
{
  "data": {
    "id": "1",
    "name": "John Doe",
    "email": "<EMAIL>",
    "role": "vendor",
    "roles": ["buyer", "vendor"],
    "verification_status": {
      "buyer": true,
      "vendor": true,
      "admin": false
    }
  }
}
```

## Configuration Options

### Session Provider Options

- `autoRefreshInterval`: Time between automatic refreshes (default: 5 minutes)
- `enableAutoRefresh`: Enable/disable automatic refresh (default: true)

### Session Service Options

- `REFRESH_COOLDOWN`: Minimum time between refresh requests (default: 2 seconds)
- `force`: Bypass cooldown and concurrent request checks
- `silent`: Perform refresh without console logging

## Best Practices

1. **Use appropriate refresh methods**:
   - `refreshSession()`: For user-initiated refreshes with UI feedback
   - `refreshSessionSilently()`: For background updates without UI feedback
   - `forceRefreshSession()`: Only when you need to bypass cooldowns

2. **Trigger updates after relevant API calls**:
   - Always call trigger functions after successful API calls that change user data
   - Use specific trigger functions (`triggerVerificationUpdate`, `triggerRoleUpdate`, etc.)

3. **Handle errors gracefully**:
   - Session refresh failures should not break the user experience
   - Provide fallback mechanisms for critical functionality

4. **Optimize refresh frequency**:
   - Don't refresh too frequently to avoid unnecessary API calls
   - Use the built-in cooldown mechanisms

## Troubleshooting

### Common Issues

1. **Session not updating**: Check if the `/api/user` endpoint is returning updated data
2. **Too many refresh requests**: Ensure you're using the cooldown mechanisms properly
3. **Token expiration**: The system handles expired tokens by signing out the user
4. **Event listeners not working**: Make sure to call the cleanup function returned by `setupSessionEventListeners`

### Debug Mode

Enable debug logging by setting `silent: false` in refresh options:

```tsx
await sessionService.refreshSession({ silent: false });
```

## Security Considerations

- The system respects token expiration and automatically signs out users with invalid tokens
- Refresh operations use the existing access token, not credentials
- All API calls include proper authentication headers
- Session data is validated before updating the client-side session

## Performance

- Automatic refresh is throttled to prevent excessive API calls
- Concurrent refresh requests are deduplicated
- Visibility change detection optimizes refresh timing
- Silent refresh operations don't impact UI performance
