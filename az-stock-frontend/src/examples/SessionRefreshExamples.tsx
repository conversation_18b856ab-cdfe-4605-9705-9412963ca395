"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useSessionContext } from "@/components/SessionProvider";
import useSessionRefresh from "@/hooks/useSessionRefresh";
import {
  triggerVerificationUpdate,
  triggerRoleUpdate,
  triggerProfileUpdate,
  setupSessionEventListeners,
} from "@/utils/sessionUtils";

/**
 * Example component showing how to use the session refresh system
 */
const SessionRefreshExamples: React.FC = () => {
  const { data: session } = useSession();
  const { refreshSession, isRefreshing, lastRefreshTime } = useSessionContext();
  const { refreshSessionSilently, forceRefreshSession } = useSessionRefresh();
  const [message, setMessage] = useState<string>("");

  // Example: Setup event listeners for session updates
  useEffect(() => {
    const cleanup = setupSessionEventListeners({
      onVerificationUpdate: () => {
        setMessage("Verification status updated!");
        setTimeout(() => setMessage(""), 3000);
      },
      onRoleUpdate: () => {
        setMessage("User roles updated!");
        setTimeout(() => setMessage(""), 3000);
      },
      onProfileUpdate: () => {
        setMessage("Profile updated!");
        setTimeout(() => setMessage(""), 3000);
      },
    });

    return cleanup;
  }, []);

  // Example: Manual refresh session
  const handleManualRefresh = async () => {
    try {
      await refreshSession();
      setMessage("Session refreshed manually!");
      setTimeout(() => setMessage(""), 3000);
    } catch (error) {
      setMessage("Failed to refresh session");
      setTimeout(() => setMessage(""), 3000);
    }
  };

  // Example: Silent refresh (no UI feedback)
  const handleSilentRefresh = async () => {
    await refreshSessionSilently();
  };

  // Example: Force refresh (bypasses cooldowns)
  const handleForceRefresh = async () => {
    try {
      await forceRefreshSession();
      setMessage("Session force refreshed!");
      setTimeout(() => setMessage(""), 3000);
    } catch (error) {
      setMessage("Failed to force refresh session");
      setTimeout(() => setMessage(""), 3000);
    }
  };

  // Example: Simulate verification update
  const handleSimulateVerificationUpdate = async () => {
    // This would typically be called after a successful verification API call
    await triggerVerificationUpdate();
  };

  // Example: Simulate role update
  const handleSimulateRoleUpdate = async () => {
    // This would typically be called after a successful role change API call
    await triggerRoleUpdate();
  };

  // Example: Simulate profile update
  const handleSimulateProfileUpdate = async () => {
    // This would typically be called after a successful profile update API call
    await triggerProfileUpdate();
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Session Refresh System Examples</h1>

      {/* Current Session Info */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Current Session Info</h2>
        <div className="space-y-2 text-sm">
          <p><strong>User:</strong> {session?.user?.name || "Not logged in"}</p>
          <p><strong>Email:</strong> {session?.user?.email || "N/A"}</p>
          <p><strong>Role:</strong> {session?.user?.role || "N/A"}</p>
          <p><strong>Roles:</strong> {session?.user?.roles?.join(", ") || "N/A"}</p>
          <p><strong>Verification Status:</strong></p>
          <ul className="ml-4">
            <li>Buyer: {session?.user?.verification_status?.buyer ? "✅" : "❌"}</li>
            <li>Vendor: {session?.user?.verification_status?.vendor ? "✅" : "❌"}</li>
            <li>Admin: {session?.user?.verification_status?.admin ? "✅" : "❌"}</li>
          </ul>
          <p><strong>Last Refresh:</strong> {lastRefreshTime ? new Date(lastRefreshTime).toLocaleString() : "Never"}</p>
          <p><strong>Refreshing:</strong> {isRefreshing ? "Yes" : "No"}</p>
        </div>
      </div>

      {/* Status Message */}
      {message && (
        <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
          {message}
        </div>
      )}

      {/* Manual Refresh Controls */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">Manual Refresh Controls</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={handleManualRefresh}
            disabled={isRefreshing}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isRefreshing ? "Refreshing..." : "Manual Refresh"}
          </button>
          
          <button
            onClick={handleSilentRefresh}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Silent Refresh
          </button>
          
          <button
            onClick={handleForceRefresh}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Force Refresh
          </button>
        </div>
      </div>

      {/* Trigger Update Events */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">Trigger Update Events</h2>
        <p className="text-sm text-gray-600 mb-4">
          These simulate events that would trigger session updates in real scenarios:
        </p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={handleSimulateVerificationUpdate}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          >
            Simulate Verification Update
          </button>
          
          <button
            onClick={handleSimulateRoleUpdate}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Simulate Role Update
          </button>
          
          <button
            onClick={handleSimulateProfileUpdate}
            className="px-4 py-2 bg-teal-500 text-white rounded hover:bg-teal-600"
          >
            Simulate Profile Update
          </button>
        </div>
      </div>

      {/* Integration Examples */}
      <div className="bg-white border rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Integration Examples</h2>
        <div className="space-y-4 text-sm">
          <div>
            <h3 className="font-medium">After User Verification API Call:</h3>
            <pre className="bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
{`// In your verification form submit handler
const handleVerificationSubmit = async (data) => {
  try {
    await api.post('/admin/users/123/verify', data);
    // Trigger session refresh to get updated verification status
    await triggerVerificationUpdate();
  } catch (error) {
    // Handle error
  }
};`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium">After Role Change API Call:</h3>
            <pre className="bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
{`// In your role management component
const handleRoleChange = async (userId, newRoles) => {
  try {
    await api.put('/admin/users/' + userId, { roles: newRoles });
    // Trigger session refresh to get updated roles
    await triggerRoleUpdate();
  } catch (error) {
    // Handle error
  }
};`}
            </pre>
          </div>

          <div>
            <h3 className="font-medium">After Profile Update API Call:</h3>
            <pre className="bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
{`// In your profile form component
const handleProfileUpdate = async (profileData) => {
  try {
    await api.put('/user/profile', profileData);
    // Trigger session refresh to get updated profile data
    await triggerProfileUpdate();
  } catch (error) {
    // Handle error
  }
};`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionRefreshExamples;
