"use client";

import { sessionService } from "@/services/SessionService";

/**
 * Utility functions for triggering session updates in various scenarios
 */

/**
 * Trigger session refresh after user verification status changes
 */
export const triggerVerificationUpdate = async (): Promise<void> => {
  try {
    // Refresh session to get updated verification status
    await sessionService.forceRefreshSession();
    
    // Dispatch custom event to notify components
    const event = new CustomEvent('verification-updated', {
      detail: { timestamp: Date.now() }
    });
    window.dispatchEvent(event);
    
    console.log("Verification status updated and session refreshed");
  } catch (error) {
    console.error("Failed to update verification status:", error);
  }
};

/**
 * Trigger session refresh after role changes
 */
export const triggerRoleUpdate = async (): Promise<void> => {
  try {
    // Refresh session to get updated roles
    await sessionService.forceRefreshSession();
    
    // Dispatch custom event to notify components
    const event = new CustomEvent('role-updated', {
      detail: { timestamp: Date.now() }
    });
    window.dispatchEvent(event);
    
    console.log("User roles updated and session refreshed");
  } catch (error) {
    console.error("Failed to update user roles:", error);
  }
};

/**
 * Trigger session refresh after profile updates
 */
export const triggerProfileUpdate = async (): Promise<void> => {
  try {
    // Refresh session to get updated profile data
    await sessionService.forceRefreshSession();
    
    // Dispatch custom event to notify components
    const event = new CustomEvent('profile-updated', {
      detail: { timestamp: Date.now() }
    });
    window.dispatchEvent(event);
    
    console.log("User profile updated and session refreshed");
  } catch (error) {
    console.error("Failed to update user profile:", error);
  }
};

/**
 * Generic function to trigger session refresh with custom event
 */
export const triggerSessionUpdate = async (eventName: string, eventData?: any): Promise<void> => {
  try {
    // Refresh session
    await sessionService.forceRefreshSession();
    
    // Dispatch custom event
    const event = new CustomEvent(eventName, {
      detail: { timestamp: Date.now(), ...eventData }
    });
    window.dispatchEvent(event);
    
    console.log(`Session updated with event: ${eventName}`);
  } catch (error) {
    console.error(`Failed to trigger session update for event ${eventName}:`, error);
  }
};

/**
 * Check if user has specific verification status
 */
export const checkVerificationStatus = async (role: "admin" | "vendor" | "buyer"): Promise<boolean> => {
  try {
    return await sessionService.isVerifiedFor(role);
  } catch (error) {
    console.error("Failed to check verification status:", error);
    return false;
  }
};

/**
 * Check if user has specific role
 */
export const checkUserRole = async (role: "admin" | "vendor" | "buyer"): Promise<boolean> => {
  try {
    return await sessionService.hasRole(role);
  } catch (error) {
    console.error("Failed to check user role:", error);
    return false;
  }
};

/**
 * Get current session data
 */
export const getCurrentSessionData = async () => {
  try {
    return await sessionService.getCurrentSession();
  } catch (error) {
    console.error("Failed to get current session data:", error);
    return null;
  }
};

/**
 * Update session with immediate data (useful after API calls that change user data)
 */
export const updateSessionWithData = async (data: {
  user?: {
    id?: string;
    name?: string;
    email?: string;
    role?: "admin" | "vendor" | "buyer";
    roles?: ("admin" | "vendor" | "buyer")[];
    verification_status?: {
      buyer?: boolean;
      vendor?: boolean;
      admin?: boolean;
    };
  };
  accessToken?: string;
}): Promise<void> => {
  try {
    await sessionService.updateSessionData(data);
    console.log("Session updated with immediate data");
  } catch (error) {
    console.error("Failed to update session with immediate data:", error);
  }
};

/**
 * Refresh session silently (without console logs)
 */
export const refreshSessionSilently = async (): Promise<void> => {
  try {
    await sessionService.refreshSessionSilently();
  } catch (error) {
    // Silent refresh, don't log errors
  }
};

/**
 * Force refresh session (bypasses cooldowns and concurrent request checks)
 */
export const forceRefreshSession = async (): Promise<void> => {
  try {
    await sessionService.forceRefreshSession();
    console.log("Session force refreshed");
  } catch (error) {
    console.error("Failed to force refresh session:", error);
  }
};

/**
 * Setup event listeners for session updates
 */
export const setupSessionEventListeners = (callbacks: {
  onVerificationUpdate?: () => void;
  onRoleUpdate?: () => void;
  onProfileUpdate?: () => void;
  onSessionUpdate?: (eventName: string, data?: any) => void;
}): (() => void) => {
  const {
    onVerificationUpdate,
    onRoleUpdate,
    onProfileUpdate,
    onSessionUpdate,
  } = callbacks;

  const handleVerificationUpdate = () => {
    if (onVerificationUpdate) onVerificationUpdate();
    if (onSessionUpdate) onSessionUpdate('verification-updated');
  };

  const handleRoleUpdate = () => {
    if (onRoleUpdate) onRoleUpdate();
    if (onSessionUpdate) onSessionUpdate('role-updated');
  };

  const handleProfileUpdate = () => {
    if (onProfileUpdate) onProfileUpdate();
    if (onSessionUpdate) onSessionUpdate('profile-updated');
  };

  // Add event listeners
  window.addEventListener('verification-updated', handleVerificationUpdate);
  window.addEventListener('role-updated', handleRoleUpdate);
  window.addEventListener('profile-updated', handleProfileUpdate);

  // Return cleanup function
  return () => {
    window.removeEventListener('verification-updated', handleVerificationUpdate);
    window.removeEventListener('role-updated', handleRoleUpdate);
    window.removeEventListener('profile-updated', handleProfileUpdate);
  };
};

export default {
  triggerVerificationUpdate,
  triggerRoleUpdate,
  triggerProfileUpdate,
  triggerSessionUpdate,
  checkVerificationStatus,
  checkUserRole,
  getCurrentSessionData,
  updateSessionWithData,
  refreshSessionSilently,
  forceRefreshSession,
  setupSessionEventListeners,
};
