import Credentials<PERSON>rovider from "next-auth/providers/credentials";
import { NextAuthOptions } from "next-auth";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: "signin",
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        token: { label: "Token", type: "text" },
      },
      //@ts-ignore
      async authorize(credentials) {
        if (!credentials) {
          console.error("No credentials provided");
          throw new Error("Missing credentials");
        }

        // Handle OTP authentication
        if (credentials.token && credentials.email) {
          try {
            console.log("OTP authentication attempt for:", credentials.email);

            // Fetch user data using the provided token
            const response = await fetch(`${process.env.NEXT_PUBLIC_AUTH_URL}/user`, {
              headers: {
                "Authorization": `Bearer ${credentials.token}`,
                "Content-Type": "application/json",
              },
            });

            if (!response.ok) {
              console.error("Failed to fetch user data with OTP token:", response.statusText);
              return null;
            }

            const responseData = await response.json();
            console.log("OTP user data response:", responseData);

            const userData = responseData.data || responseData;

            return {
              id: userData.id.toString(),
              accessToken: credentials.token,
              email: userData.email,
              name: userData.name,
              role: userData.role || userData.roles?.[0],
              roles: userData.roles || [userData.role],
              verification_status: userData.verification_status,
            };
          } catch (error) {
            console.error("OTP authorization error:", error);
            return null;
          }
        }

        // Handle password authentication
        const { email, password } = credentials;
console.log(`${process.env.NEXT_PUBLIC_AUTH_URL}/login`)
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_AUTH_URL}/login`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, password }),
          });

          if (!response.ok) {
            console.error("Failed to authenticate:", response.statusText);
            throw new Error("Invalid credentials");
          }

          const responseData = await response.json();
          console.log("Login response:", responseData);

          // Ensure the data has a token and that the response structure is correct
          if (!responseData.token) {
            console.error("Token missing in response:", responseData);
            return null;
          }

          // Extract user data from the nested data object
          const userData = responseData.data;

          console.log("User data from API:", userData);
          console.log("User role:", userData.role);

          return {
            id: userData.id.toString(), // Store user ID
            accessToken: responseData.token, // Store the access token
            email: userData.email,
            name: userData.name,
            role: userData.role || userData.roles?.[0], // Legacy role support
            roles: userData.roles || [userData.role], // Multi-role support
            verification_status: userData.verification_status,
          };
        } catch (error) {
          console.error("Authorization error:", error);
          return null;
        }
      },
    }),
  ],

  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/login",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.accessToken = user.accessToken;
        //@ts-ignore
        token.role = user.role;
        //@ts-ignore
        token.roles = user.roles;
        //@ts-ignore
        token.verification_status = user.verification_status;
      }

      return token;
    },

    async session({ session, token }) {
      session.user = {
        //@ts-ignore
        id: token.id,
        email: token.email,
        name: token.name,
        //@ts-ignore
        role: token.role,
        //@ts-ignore
        roles: token.roles,
        //@ts-ignore
        verification_status: token.verification_status,
      };
      //@ts-ignore
      session.accessToken = token.accessToken;

      return session;
    },
  },
};
