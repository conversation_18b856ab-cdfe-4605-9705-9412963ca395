import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";
import { NextAuthOptions } from "next-auth";

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      id: "signin",
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      //@ts-ignore
      async authorize(credentials) {
        if (!credentials) {
          console.error("No credentials provided");
          throw new Error("Missing credentials");
        }

        const { email, password } = credentials;
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_AUTH_URL}/login`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, password }),
          });

          if (!response.ok) {
            console.error("Failed to authenticate:", response.statusText);
            throw new Error("Invalid credentials");
          }

          const responseData = await response.json();
          console.log("Login response:", responseData);

          // Ensure the data has a token and that the response structure is correct
          if (!responseData.token) {
            console.error("Token missing in response:", responseData);
            return null;
          }

          // Extract user data from the nested data object
          const userData = responseData.data;
          console.log("User data from API:", userData);
          console.log("User role:", userData.role);

          return {
            id: userData.id.toString(), // Store user ID
            accessToken: responseData.token, // Store the access token
            email: userData.email,
            name: userData.name,
            role: userData.role || userData.roles?.[0], // Legacy role support
            roles: userData.roles || [userData.role], // Multi-role support
            verification_status: userData.verification_status,
            is_verified_vendor: userData.is_verified_vendor,
            is_verified_buyer: userData.is_verified_buyer,
            is_verified_admin: userData.is_verified_admin,
          };
        } catch (error) {
          console.error("Authorization error:", error);
          return null;
        }
      },
    }),
  ],

  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/login",
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      // Initial sign in
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.accessToken = user.accessToken;
        //@ts-ignore
        token.role = user.role;
        //@ts-ignore
        token.roles = user.roles;
        //@ts-ignore
        token.verification_status = user.verification_status;
        token.lastRefresh = Date.now();
      }

      // Handle session update trigger
      if (trigger === "update" && session) {
        // Update token with new session data
        if (session.user) {
          token.name = session.user.name || token.name;
          token.email = session.user.email || token.email;
          //@ts-ignore
          token.role = session.user.role || token.role;
          //@ts-ignore
          token.roles = session.user.roles || token.roles;
          //@ts-ignore
          token.verification_status = session.user.verification_status || token.verification_status;
        }
        if (session.accessToken) {
          //@ts-ignore
          token.accessToken = session.accessToken;
        }
        token.lastRefresh = Date.now();
      }

      // Auto-refresh user data if token is older than 5 minutes and we have an access token
      const shouldRefresh = token.accessToken &&
        token.lastRefresh &&
        Date.now() - (token.lastRefresh as number) > 5 * 60 * 1000; // 5 minutes

      if (shouldRefresh) {
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/user`, {
            headers: {
              'Authorization': `Bearer ${token.accessToken}`,
              'Content-Type': 'application/json',
            },
          });

          if (response.ok) {
            const userData = await response.json();
            // Update token with fresh user data
            token.name = userData.data?.name || token.name;
            token.email = userData.data?.email || token.email;
            token.role = userData.data?.role || token.role;
            token.roles = userData.data?.roles || token.roles;
            token.verification_status = userData.data?.verification_status || token.verification_status;
            token.lastRefresh = Date.now();
          }
        } catch (error) {
          console.warn('Failed to refresh user data:', error);
          // Keep existing token data on error
        }
      }

      return token;
    },

    async session({ session, token }) {
      session.user = {
        //@ts-ignore
        id: token.id,
        email: token.email,
        name: token.name,
        //@ts-ignore
        role: token.role,
        //@ts-ignore
        roles: token.roles,
        //@ts-ignore
        verification_status: token.verification_status,
      };
      //@ts-ignore
      session.accessToken = token.accessToken;

      return session;
    },
  },
};
