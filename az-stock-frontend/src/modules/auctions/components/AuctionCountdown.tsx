"use client";
import { useState, useEffect } from "react";

interface AuctionCountdownProps {
  endTime: string;
  className?: string;
  showLabel?: boolean;
  labelText?: string;
}

const AuctionCountdown: React.FC<AuctionCountdownProps> = ({
  endTime,
  className = "",
  showLabel = false,
  labelText = "Time Remaining:",
}) => {
  const [timeRemaining, setTimeRemaining] = useState<string>("");

  const calculateTimeRemaining = (endTime: string): string => {
    const end = new Date(endTime);
    const now = new Date();
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return "Ended";

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m ${seconds}s`;
    return `${minutes}m ${seconds}s`;
  };

  useEffect(() => {
    const updateCountdown = () => {
      setTimeRemaining(calculateTimeRemaining(endTime));
    };

    // Initial calculation
    updateCountdown();

    // Set up interval to update every second
    const intervalId = setInterval(updateCountdown, 1000);

    // Cleanup interval on unmount
    return () => clearInterval(intervalId);
  }, [endTime]);

  const isEnded = timeRemaining === "Ended";
  const textColorClass = isEnded ? "text-red-600" : "text-blue-600";

  if (showLabel) {
    return (
      <div className={`flex justify-between items-center ${className}`}>
        <span className="text-sm text-gray-500">{labelText}</span>
        <span className={`text-sm font-medium ${textColorClass}`}>
          {timeRemaining}
        </span>
      </div>
    );
  }

  return (
    <span className={`font-medium ${textColorClass} ${className}`}>
      {timeRemaining}
    </span>
  );
};

export default AuctionCountdown;
