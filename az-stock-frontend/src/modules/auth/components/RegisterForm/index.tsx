"use client";
import Container from "@/modules/common/components/Container";
import GlobalInput from "@/modules/common/components/GlobaInput";
import GlobalButton from "@/modules/common/components/GlobalButton";
import GlobalForm from "@/modules/common/components/GlobalForm";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { IoCloseCircle } from "react-icons/io5";
import Link from "next/link";
import { z } from "zod";
import GlobalSelect from "@/modules/common/components/GlobalSelect";

const schema = z
  .object({
    name: z.string().min(3, "Name should contain at least 3 characters"),
    email: z.string().email("Please enter a valid email address"),
    password: z
      .string()
      .min(5, "Password should contain at least 5 characters"),
    password_confirmation: z
      .string()
      .min(5, "Password confirmation is required"),
    roles: z
      .array(z.enum(["buyer", "vendor"]))
      .min(1, "Please select at least one role"),
  })
  .refine((data) => data.password === data.password_confirmation, {
    message: "Passwords do not match",
    path: ["password_confirmation"],
  });

const RegisterForm = () => {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState<string | undefined>(
    undefined
  );
  const [loading, setLoading] = useState(false);

  const onSubmit = async (data: {
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
    roles: ("buyer" | "vendor")[];
  }) => {
    try {
      setLoading(true);

      const response = await fetch("http://*************:8000/api/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        // Registration successful, redirect to login
        router.push("/login?registered=true");
      } else {
        // Handle error response
        const errorData = await response.json();
        console.error("Registration failed:", errorData);

        if (errorData.message) {
          setErrorMessage(errorData.message);
        } else if (errorData.errors) {
          // Format validation errors
          const errorMessages = Object.values(errorData.errors).flat();
          setErrorMessage(errorMessages.join(", "));
        } else {
          setErrorMessage("Registration failed. Please try again.");
        }
      }
    } catch (error) {
      console.error("An error occurred during registration:", error);
      setErrorMessage(
        "An error occurred during registration. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container
      className="bg-white rounded-lg !p-4 shadow !max-w-3xl"
      data-aos="fade-in"
    >
      <div className="flex flex-col gap-1 items-center">
        <h1 className="text-xl">Register</h1>
      </div>
      <GlobalForm
        schema={schema}
        className="flex flex-col gap-4 pt-5"
        onSubmit={(e) => {
          onSubmit(e);
        }}
      >
        <GlobalInput name="name" placeholder="John Doe" label="Full Name" />
        <GlobalInput
          name="email"
          type="email"
          placeholder="<EMAIL>"
          label="Email"
        />
        <GlobalInput
          name="password"
          type="password"
          placeholder="password"
          label="Password"
        />
        <GlobalInput
          name="password_confirmation"
          type="password"
          placeholder="confirm password"
          label="Confirm Password"
        />

        {/* Role Selection */}
        <GlobalSelect
          name="roles"
          isMulti
          data={[
            { value: "buyer", label: "Buyer " },
            {
              value: "vendor",
              label: "Vendor ",
            },
          ]}
        />
        {/* <label className="flex items-center gap-2 cursor-pointer">
          <input type="checkbox" value={"buyer"} className="checkbox " />
          <span>{"buyer"}</span>
        </label>
        <label className="flex items-center gap-2 cursor-pointer">
          <input type="checkbox" value={"vendor"} className="checkbox " />
          <span>{"vendor"}</span>
        </label> */}
        {/* <GlobalInput
          name="roles"
          type="checkbox"
          label="Select Your Role(s) *"
          options={[
            { value: "buyer", label: "Buyer - Place bids and purchase items" },
            {
              value: "vendor",
              label: "Vendor - Sell products and create auctions",
            },
          ]}
          helperText="You can select multiple roles. New users require admin verification before performing key actions."
        /> */}

        <div className="flex flex-col gap-2">
          <GlobalButton
            loading={loading}
            onClick={() => {
              setLoading(true);
            }}
          >
            Register
          </GlobalButton>
          <div className="text-center mt-2">
            Already have an account?{" "}
            <Link href="/login" className="text-blue-500 hover:underline">
              Login
            </Link>
          </div>
        </div>
      </GlobalForm>
      <div className="mt-10">
        {errorMessage && (
          <div className="alert alert-error shadow-lg">
            <div className="flex gap-3">
              <IoCloseCircle
                size="20"
                color="#000"
                onClick={() => {
                  setErrorMessage(undefined);
                }}
              />
              <span>{errorMessage}</span>
            </div>
          </div>
        )}
      </div>
    </Container>
  );
};

export default RegisterForm;
