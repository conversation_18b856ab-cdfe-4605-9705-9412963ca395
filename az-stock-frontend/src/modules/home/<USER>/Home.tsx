"use client";
import { AuctionModel } from "@/modules/auctions/models/AuctionModel";
import AuctionList from "@/modules/auctions/components/AuctionList";
import Container from "@/modules/common/components/Container";
import { ReactNode, useState } from "react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import GlobalLink from "@/modules/common/components/GlobalLink";
import { AiFillGold } from "react-icons/ai";
import { BiCar, BiCloset, BiHome, BiSolidPalette } from "react-icons/bi";
import { BsGem } from "react-icons/bs";
import { MdComputer } from "react-icons/md";

interface HomeProps {
  auctions?: AuctionModel[];
  categories?: Array<{ id: number; name: string }>;
}

const Home: React.FC<HomeProps> = ({ auctions = [], categories = [] }) => {
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const t = useTranslations("home");

  // Make sure auctions is an array before filtering
  const auctionsArray = Array.isArray(auctions) ? auctions : [];

  const filteredAuctions = selectedCategory
    ? auctionsArray.filter(
        (auction) => auction.category?.id === selectedCategory
      )
    : auctionsArray;

  return (
    <>
      {/* <Slider /> */}
      <div className="bg-gray-100 py-10">
        <Container className="flex items-center justify-between xs:flex-col-reverse gap-10">
          <div>
            <h1 className="text-4xl font-semibold">
              Discover Unique Items at Az-Stock
            </h1>
            <p className="py-4">
              Join thounsands of buyers and sellers in our trusted auction
              marketplace. Bid on exclusive items or start your own auction
              today.
            </p>
            <div className="flex items-center gap-5">
              <GlobalLink
                href="/auctions"
                className="btn bg-black text-white hover:text-black"
              >
                Browse Auctions
              </GlobalLink>
              <GlobalLink href="/Dashboard" className="btn">
                Start selling
              </GlobalLink>
            </div>
          </div>
          <div className="md:w-1/2">
            <GlobalImage
              src="/home.png"
              alt=""
              width={500}
              height={500}
              className="w-full rounded-xl"
            />
          </div>
        </Container>
      </div>
      <Container className="py-20">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2 xs:text-xl">
              {t("Live Auctions")}
            </h1>
            <p className="text-gray-600 xs:text-sm">
              {t("Discover unique items and place your bids")}
            </p>
          </div>

          <div className="mt-4 md:mt-0">
            <Link
              href="/auctions"
              className="bg-gray-800 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded xs:text-sm"
            >
              {t("View All Auctions")}
            </Link>
          </div>
        </div>

        {Array.isArray(categories) && categories.length > 0 && (
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => setSelectedCategory(null)}
                className={`px-4 py-2 rounded-full text-sm font-medium xs:text-xs xs:px-2 ${
                  selectedCategory === null
                    ? "bg-gray-800 text-white"
                    : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                }`}
              >
                {t("All Categories")}
              </button>

              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium xs:text-xs xs:px-2 ${
                    selectedCategory === category.id
                      ? "bg-gray-800 text-white"
                      : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        )}

        {filteredAuctions.length > 0 ? (
          <AuctionList auctions={filteredAuctions} />
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-medium text-gray-600">
              No auctions found in this category
            </h3>
            <p className="mt-2 text-gray-500">
              Try selecting a different category or check back later
            </p>
          </div>
        )}
      </Container>

      <div className="bg-gray-100 py-20">
        <Container className="flex flex-col gap-4 justify-center items-center">
          <h2 className="text-3xl font-medium mb-2 xs:text-xl">
            Browse by Category
          </h2>
          <p>Find exactly what you're looking for to start bidding today!</p>
          <div className="flex gap-10 flex-wrap items-center justify-center py-10">
            <CategoryCard name="Jewelry" icon={<BiSolidPalette />} />
            <CategoryCard name="Art" icon={<BsGem />} />
            <CategoryCard name="Electronics" icon={<MdComputer />} />
            <CategoryCard name="Vehicles" icon={<BiCar />} />
            <CategoryCard name="Home" icon={<BiHome />} />
            <CategoryCard name="Fashion" icon={<BiCloset />} />
          </div>
        </Container>
      </div>

      <Container className="flex flex-col gap-4 justify-center items-center !py-20">
        <h2 className="text-3xl font-medium mb-2 xs:text-xl">How it Works</h2>
        <p>Simple steps to start bidding today!</p>
        <div className="grid grid-cols-3 gap-40 xs:gap-8 pt-5 xs:grid-cols-1">
          <div className="flex flex-col items-center text-center gap-2">
            <span className="p-4 bg-gray-100 rounded-full w-14 h-14 flex items-center justify-center">
              1
            </span>
            <h4>Register & Browse</h4>
            <p className="text-sm text-gray-700 max-w-sm">
              Create your account and explore thousends of auctions various
              categories
            </p>
          </div>

          <div className="flex flex-col items-center gap-2 text-center">
            <span className="p-4 bg-gray-100 rounded-full w-14 h-14 flex items-center justify-center">
              2
            </span>
            <h4>Bid or List</h4>
            <p className="text-sm text-gray-700  max-w-sm">
              Pleace competitive bids on items you want ot list your own items
              of auctions
            </p>
          </div>

          <div className="flex flex-col items-center gap-2 text-center">
            <span className="p-4 bg-gray-100 rounded-full w-14 h-14 flex items-center justify-center">
              3
            </span>
            <h4>Win & Complete</h4>
            <p className="text-sm text-gray-700  max-w-sm">
              Win auctions and complete source transactions through our trusted
              payment system
            </p>
          </div>
        </div>
      </Container>
    </>
  );
};

export default Home;

const CategoryCard = ({ icon, name }: { icon?: ReactNode; name: string }) => {
  return (
    <div className="bg-white rounded-lg flex flex-col items-center justify-center gap-2 p-4 min-w-[140px] min-h-[90px]">
      {icon}
      <span>{name}</span>
    </div>
  );
};
