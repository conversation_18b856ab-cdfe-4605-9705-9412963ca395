"use client";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";
import GlobalHorizontalSlider from "@/modules/common/components/GlobalSlider/GlobalSlider";
import { useTranslations } from "next-intl";

const Slider = () => {
  const t = useTranslations("home");

  return (
    <GlobalHorizontalSlider>
      {" "}
      <div className=" !w-[100vw]  text-white relative flex flex-col justify-center">
        <GlobalImage
          alt=""
          width={400}
          height={400}
          src={"/store.png"}
          className="absolute -z-10 w-full xs:object-cover xs:h-full"
        />
        <div className="w-screen p-16 xs:p-5">
          <h2 className="font-bold text-xl xs:text-sm">
            {t("Welcome to AZ-Stock")}
          </h2>
          <p className="xs:text-xs">{t("Direct access")}</p>
        </div>
      </div>
      <div className=" !w-[100vw]  text-white relative flex flex-col justify-center">
        <GlobalImage
          alt=""
          width={400}
          height={400}
          src={"/store.png"}
          className="absolute -z-10 w-full xs:h-full xs:object-cover"
        />
        <div className="w-screen p-16 xs:p-5">
          <h2 className="font-bold text-xl xs:text-sm">
            Contract Agreements and Direct Sales Now Available
          </h2>
          <p className="xs:text-xs">
            Looking for a long-term sourcing solution? Lock in inventory at
            consistent and high volumes from top sellers on B-Stock Direct!
          </p>
        </div>
      </div>
      <div className=" !w-[100vw]  text-white relative flex flex-col justify-center">
        <GlobalImage
          alt=""
          src={"/store.png"}
          width={400}
          height={400}
          className="absolute -z-10 w-full xs:h-full xs:object-cover"
        />
        <div className="w-screen p-16 xs:p-5">
          <h2 className="font-bold text-xl xs:text-sm">
            Shop Electronics, Wearables, and Appliances at Best Buy
          </h2>
          <p className="xs:text-xs">
            The Best Buy storefront offers inventory from some of the most
            trusted consumer brands and manufacturers. Check out new product
            categories available now!
          </p>
        </div>
      </div>
    </GlobalHorizontalSlider>
  );
};
export default Slider;
