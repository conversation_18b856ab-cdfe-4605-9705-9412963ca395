"use client";
import { useLocale } from "next-intl";
import { useRouter, usePathname, Link } from "../../../../i18n/navigation";
import React, { ChangeEvent, useEffect, useState } from "react";
import { useParams } from "next/navigation";

const LangSwicher = () => {
  const locale = useLocale();
  const path = usePathname();
  const params = useParams();
  const router = useRouter();

  return (
    <div className="flex gap-[4px]">
      {locale === "ar" ? (
        <Link locale="en" scroll={false} className={``} href={`/${path}`}>
          EN
        </Link>
      ) : (
        <Link
          locale="ar"
          className={`${
            locale === "ar" ? "font-[700] pointer-events-none" : " "
          }`}
          href={`/${path}`}
        >
          AR
        </Link>
      )}
    </div>
  );
};

export default LangSwicher;
