"use client";

import { useState } from "react";
import Container from "../Container";
import Image from "next/image";
import { useSession } from "next-auth/react";
import { BiMenu } from "react-icons/bi";
import LangSwicher from "../LangSwicher";
import GlobalLink from "../GlobalLink";
import { useTranslations } from "next-intl";

export default function Header() {
  const { status } = useSession();
  const isAuthenticated = status === "authenticated";
  const [showBuyerDropdown, setShowBuyerDropdown] = useState(false);
  const [showSellerDropdown, setShowSellerDropdown] = useState(false);
  const t = useTranslations("general");

  return (
    <Container className="flex justify-between items-center gap-2 ">
      <div className="flex">
        {/* Replace this with your actual logo component or img */}
        <GlobalLink href={"/"} className="w-28 h-auto">
          <Image
            width={100}
            height={100}
            src="/logo.png"
            alt="Logo"
            className="w-full h-auto"
          />
        </GlobalLink>
      </div>

      <nav className="flex items-center justify-end font-semibold gap-5 xs:dropdown xs:dropdown-end xs:dropdown-bottom md:dropdown-open">
        <div tabIndex={0} role="button" className=" m-1 md:hidden">
          <BiMenu size={28} />
        </div>
        <ul
          tabIndex={0}
          className="dropdown-content menu bg-base-100 rounded-box z-[1] xs:w-52 xs:p-2 xs:shadow md:gap-5 md:flex-row md:items-center md:justify-end"
        >
          <GlobalLink href="/auctions">{t("Shop All Auctions")}</GlobalLink>

          {/* Buyers Dropdown */}
          <div
            className="relative"
            onMouseEnter={() => setShowBuyerDropdown(true)}
            onMouseLeave={() => setShowBuyerDropdown(false)}
          >
            <div tabIndex={0} role="button" className="m-1 cursor-pointer">
              {t("For Buyers")}
            </div>
            {showBuyerDropdown && (
              <ul className="absolute dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                <li>
                  <GlobalLink href="/how-it-works">
                    {t("How it works")}
                  </GlobalLink>
                </li>
                <li>
                  <GlobalLink href="/buyer-resource-center">
                    {t("Buyer resource center")}
                  </GlobalLink>
                </li>
              </ul>
            )}
          </div>

          {/* Sellers Dropdown */}
          <div
            className="relative"
            onMouseEnter={() => setShowSellerDropdown(true)}
            onMouseLeave={() => setShowSellerDropdown(false)}
          >
            <div tabIndex={0} role="button" className="m-1 cursor-pointer">
              {t("For Sellers")}
            </div>
            {showSellerDropdown && (
              <ul className="absolute dropdown-content  menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow">
                <li>
                  <GlobalLink href="/sellers">
                    {t("Want to sell with us?")}
                  </GlobalLink>
                </li>
                <li>
                  <GlobalLink href="/sellers/enterprise">
                    {t("Enterprise seller")}
                  </GlobalLink>
                </li>
              </ul>
            )}
          </div>

          {isAuthenticated ? (
            <GlobalLink href="/dashboard">{t("Dashboard")}</GlobalLink>
          ) : (
            <>
              <GlobalLink href="/login">{t("Login")}</GlobalLink>
              <GlobalLink href="/register">{t("Register")}</GlobalLink>
            </>
          )}
          <li>
            <LangSwicher />
          </li>
        </ul>
      </nav>
    </Container>
  );
}
