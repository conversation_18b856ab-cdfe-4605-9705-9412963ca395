"use client";
import React, { ReactNode, useEffect, useState } from "react";
import { AiOutlineLeft, AiOutlineRight } from "react-icons/ai";
import { EmblaOptionsType } from "embla-carousel";
import useEmblaCarousel from "embla-carousel-react";
import { WheelGesturesPlugin } from "embla-carousel-wheel-gestures";
import GlobalImage from "../GlobalImage/GlobalImage";
import Fade from "embla-carousel-fade";
import Autoplay from "embla-carousel-fade";
import { useRouter } from "next/navigation";
import { BsArrowLeft, BsArrowRight } from "react-icons/bs";
import { AuctionItemImageModel } from "@/types/auction";
import { useLocale } from "next-intl";
interface GlobalHorizontalSliderProps {
  list?: string[];
  pictures?: string[];
  showSmallPictures?: boolean;
  options?: EmblaOptionsType;
  hasButtons?: boolean;
  scrollHorizontally?: boolean;
  slides?: "1" | "2" | "3" | "4";
  mobileSlides?: "1" | "2" | "3" | "4";
  slideGap?: string;
  showDots?: boolean;
  children?: ReactNode;
  autoplay?: boolean;
  buttonsCenter?: boolean;
}

const GlobalHorizontalSlider = ({
  list,
  pictures,
  hasButtons,
  showDots = false,
  slides,
  mobileSlides,
  slideGap,
  scrollHorizontally,
  options,
  children,
  autoplay = true,
  buttonsCenter,
  showSmallPictures,
}: GlobalHorizontalSliderProps) => {
  const local = useLocale();
  const defaultOptions: EmblaOptionsType = {
    dragFree: false,
    direction: local === "ar" ? "rtl" : "ltr",
    containScroll: "trimSnaps",
    startIndex: 0,
    inViewThreshold: 0,
    loop: true,
  };

  const plugins = [
    WheelGesturesPlugin({
      forceWheelAxis: scrollHorizontally ? "y" : "x",
    }),
  ];

  if (autoplay) {
    plugins.push(Fade()); // if you're using a Fade plugin — optional
    plugins.push(
      Autoplay({
        //@ts-ignore
        playOnInit: true,
        delay: 2500,
        stopOnFocusIn: false,
        stopOnInteraction: false,
      })
    );
  }

  const [emblaRef, emblaApi] = useEmblaCarousel(
    options ?? defaultOptions,
    plugins
  );
  useEffect(() => {
    function handleArrowKeys(e: KeyboardEvent) {
      if (e.key === "ArrowLeft") {
        return emblaApi?.scrollPrev();
      }
      if (e.key === "ArrowRight") {
        return emblaApi?.scrollNext();
      }
    }
    document.addEventListener("keydown", handleArrowKeys);
    return () => {
      document.removeEventListener("keydown", handleArrowKeys);
    };
  }, [emblaApi]);
  const [selectedIndex, setSelectedIndex] = useState(0);

  useEffect(() => {
    if (!emblaApi) return;

    const handleSelect = () => setSelectedIndex(emblaApi.selectedScrollSnap());
    emblaApi.on("select", handleSelect);
    handleSelect(); // initial selection
  }, [emblaApi]);
  const router = useRouter();
  const extractPath = (url: string): string => {
    try {
      const parsedUrl = new URL(url); // Parse the URL
      return parsedUrl.pathname + parsedUrl.search; // Return the path and query string
    } catch (error) {
      console.error("Invalid URL:", error);
      return ""; // Return an empty string if the URL is invalid
    }
  };
  return (
    <div className=" overflow-hidden relative flex flex-col items-center justify-center w-full">
      <div
        className={`${
          hasButtons ? "" : "hidden"
        } px-4 z-50 gap-6 self-end flex justify-between`}
      >
        <div className={`flex  p-1 justify-center items-start `}>
          <BsArrowLeft
            onClick={() => emblaApi?.scrollPrev()}
            color="#4b5563"
            className={`cursor-pointer ${
              buttonsCenter && "absolute left-[12%] xs:left-8 top-1/2"
            }`}
            size={"36px"}
          />
        </div>
        <div className={`flex p-1 justify-center items-start `}>
          <BsArrowRight
            color="#4b5563"
            className={`cursor-pointer ${
              buttonsCenter && "absolute right-[12%] xs:right-8 top-1/2"
            }`}
            onClick={() => emblaApi?.scrollNext()}
            size={"36px"}
          />
        </div>
      </div>
      <div className="flex flex-col-reverse gap-2 w-full">
        {showSmallPictures && pictures && (
          <div className="flex overflow-auto mb-4 gap-2 w-auto max-h-screen md:absolute z-20  sb">
            {pictures.map((pic, index) => (
              <div
                key={index}
                onClick={() => emblaApi?.scrollTo(index)}
                className={`cursor-pointer ${
                  selectedIndex === index ? " brightness-105" : " brightness-75"
                }`}
              >
                <GlobalImage
                  src={pic}
                  width={80}
                  height={80}
                  className="object-cover md:w-20 md:h-20 xs:aspect-square"
                  alt={`Thumbnail ${index + 1}`}
                />
              </div>
            ))}
          </div>
        )}
        <div ref={emblaRef}>
          <div
            style={{ gap: `${slideGap ?? "0"}px` }}
            className={`backface-visible flex touch-pan-y relative w-full`}
          >
            {children}

            {!pictures
              ? list?.map((d, index) => {
                  return (
                    <div
                      onClick={() => {
                        const slidesInViewport = emblaApi?.slidesInView();

                        if (slidesInViewport) {
                          const firstSLide = slidesInViewport[0];
                          const endSlide =
                            slidesInViewport[slidesInViewport.length - 1];

                          if (firstSLide === index) {
                            return emblaApi?.scrollPrev();
                          }
                          if (endSlide === index) {
                            return emblaApi?.scrollNext();
                          }
                        }
                      }}
                      style={{ flex: "0 0 100%" }}
                      className={` min-w-0   ${
                        mobileSlides === "2"
                          ? "max-w-1/2"
                          : mobileSlides === "3"
                          ? "max-w-[33.3333%]"
                          : mobileSlides === "4"
                          ? "max-w-[25%]"
                          : "max-w-[500px]"
                      } ${
                        slides === "1"
                          ? "md:max-w-[100%]"
                          : slides === "2"
                          ? "md:max-w-[50%]"
                          : slides === "3"
                          ? "md:max-w-[33.3333%]"
                          : slides === "4"
                          ? "md:max-w-[25%]"
                          : ""
                      } `}
                      key={index}
                    >
                      <GlobalImage
                        height={500}
                        width={500}
                        property={index == 0 ? "100" : ""}
                        className="  object-cover md:h-[80vh]  w-full object-top"
                        src={d}
                        alt="Az stock"
                      />
                    </div>
                  );
                })
              : pictures?.map((el, index, arr) => {
                  return (
                    <div
                      style={{ flex: "0 0 100%" }}
                      className={` min-w-0  md:h-[60vh] xs:max-h-[250px] flex flex-col items-center justify-center ${
                        mobileSlides === "2"
                          ? "max-w-1/2"
                          : mobileSlides === "3"
                          ? "max-w-[33.3333%]"
                          : mobileSlides === "4"
                          ? "max-w-[25%]"
                          : ""
                      } ${
                        slides === "1"
                          ? "md:max-w-[100%]"
                          : slides === "2"
                          ? "md:max-w-[50%]"
                          : slides === "3"
                          ? "md:max-w-[33.3333%]"
                          : slides === "4"
                          ? "md:max-w-[25%]"
                          : ""
                      } `}
                      key={index}
                    >
                      <div className="w-full h-full  ">
                        <GlobalImage
                          height={200}
                          width={200}
                          loading="eager"
                          className="xs:object-top  object-contain h-full w-full "
                          src={el}
                          alt="Fashion Photographer in Miami for Celebrity Photo Shoots"
                        />
                      </div>
                    </div>
                  );
                })}
          </div>
          {showDots && (
            <Dots emblaApi={emblaApi} selectedIndex={selectedIndex} />
          )}

          {/* Next / Back buttons section */}
        </div>
      </div>
    </div>
  );
};
const Dots = ({
  emblaApi,
  selectedIndex,
}: {
  emblaApi: any;
  selectedIndex: number;
}) => {
  const [dotCount, setDotCount] = useState(0);

  useEffect(() => {
    if (!emblaApi) return;
    setDotCount(emblaApi.scrollSnapList().length);
  }, [emblaApi]);

  return (
    <div className="flex justify-center gap-2 xs:gap-1 mt-4 absolute xs:bottom-10  bottom-4 left-1/2 -translate-x-1/2">
      {Array.from({ length: dotCount }, (_, index) => (
        <span
          key={index}
          onClick={() => emblaApi.scrollTo(index)}
          className={`w-2 h-2 xs:w-1 xs:h-1 rounded-full cursor-pointer ${
            selectedIndex === index ? "bg-gray-500" : "bg-gray-200"
          }`}
        />
      ))}
    </div>
  );
};
export default GlobalHorizontalSlider;
