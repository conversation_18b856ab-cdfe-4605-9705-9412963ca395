import { GlobalFetchJson } from "@/core/api/fetcher/GlobalFetcher";
import GlobalService from "@/core/api/service/GlobalService";
import {
  AuctionModel,
  CategoryModel,
  PaginatedResponse,
  SingleResponse,
  CreateAuctionForm,
  UpdateAuctionForm,
  AuctionFilters,
  CategoryFilters,
} from "../models/DashboardModels";
import {
  AuctionModel as BaseAuctionModel,
  CreateAuctionRequest,
  SingleAuctionResponse,
  AuctionResponse,
} from "@/types/auction";

export default class AuctionService implements GlobalService {
  methods = {
    // Auction Management
    async getMyAuctions(
      filters?: AuctionFilters
    ): Promise<PaginatedResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const params = new URLSearchParams();
        if (filters?.status) params.append("status", filters.status);
        if (filters?.auction_type)
          params.append("auction_type", filters.auction_type);
        if (filters?.category_id)
          params.append("category_id", filters.category_id.toString());
        if (filters?.search) params.append("search", filters.search);
        if (filters?.ending_soon !== undefined)
          params.append("ending_soon", filters.ending_soon.toString());
        if (filters?.per_page)
          params.append("per_page", filters.per_page.toString());
        if (filters?.page) params.append("page", filters.page.toString());

        const queryString = params.toString();
        const url = `/my-auctions${queryString ? `?${queryString}` : ""}`;

        const response = await GlobalFetchJson<AuctionResponse>(
          url,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );

        // Transform response to match dashboard model structure
        return {
          data: response.data,
          links: response.links,
          // meta: response.meta,
        };
      } catch (error) {
        throw error;
      }
    },

    async getAllAuctions(
      filters?: AuctionFilters
    ): Promise<PaginatedResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const params = new URLSearchParams();
        if (filters?.status) params.append("status", filters.status);
        if (filters?.auction_type)
          params.append("auction_type", filters.auction_type);
        if (filters?.category_id)
          params.append("category_id", filters.category_id.toString());
        if (filters?.vendor_id)
          params.append("vendor_id", filters.vendor_id.toString());
        if (filters?.search) params.append("search", filters.search);
        if (filters?.min_price)
          params.append("min_price", filters.min_price.toString());
        if (filters?.max_price)
          params.append("max_price", filters.max_price.toString());
        if (filters?.ending_soon !== undefined)
          params.append("ending_soon", filters.ending_soon.toString());
        if (filters?.per_page)
          params.append("per_page", filters.per_page.toString());
        if (filters?.page) params.append("page", filters.page.toString());

        const queryString = params.toString();
        const url = `/auctions${queryString ? `?${queryString}` : ""}`;

        const response = await GlobalFetchJson<AuctionResponse>(
          url,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );

        return {
          data: response.data,
          links: response.links,
          // meta: response.meta,
        };
      } catch (error) {
        throw error;
      }
    },

    async getAuctionById(
      auctionId: number
    ): Promise<SingleResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleAuctionResponse>(
          `/auctions/${auctionId}`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );

        return {
          data: response.data,
        };
      } catch (error) {
        throw error;
      }
    },

    async createAuction(
      auctionData: CreateAuctionForm
    ): Promise<SingleResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        // Transform form data to API request format
        console.log("AuctionService received data:", auctionData);
        console.log("Featured image from form:", auctionData.featured_image);

        const requestData: CreateAuctionRequest = {
          title: auctionData.title,
          description: auctionData.description,
          category_id: auctionData.category_id,
          auction_type: auctionData.auction_type,
          start_time: auctionData.start_time,
          end_time: auctionData.end_time,
          starting_price: auctionData.starting_price,
          reserve_price: auctionData.reserve_price,
          items: auctionData.items,
        };

        // Only add featured_image if it exists
        if (auctionData.featured_image) {
          requestData.featured_image = auctionData.featured_image;
        }

        console.log("Final request data:", requestData);
        console.log("Featured image in request:", requestData.featured_image);
        console.log("Request data JSON:", JSON.stringify(requestData, null, 2));

        const response = await GlobalFetchJson<SingleAuctionResponse>(
          `/auctions`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify(requestData),
          },
          "/api/proxy"
        );

        return {
          data: response.data,
        };
      } catch (error) {
        throw error;
      }
    },

    async updateAuction(
      auctionId: number,
      auctionData: UpdateAuctionForm
    ): Promise<SingleResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleAuctionResponse>(
          `/auctions/${auctionId}`,
          {
            method: "PUT",
            headers: headers,
            body: JSON.stringify(auctionData),
          },
          "/api/proxy"
        );

        return {
          data: response.data,
        };
      } catch (error) {
        throw error;
      }
    },

    async deleteAuction(auctionId: number): Promise<{ message: string }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{ message: string }>(
          `/auctions/${auctionId}`,
          {
            method: "DELETE",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async endAuction(auctionId: number): Promise<SingleResponse<AuctionModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleAuctionResponse>(
          `/auctions/${auctionId}/end`,
          {
            method: "POST",
            headers: headers,
          },
          "/api/proxy"
        );

        return {
          data: response.data,
        };
      } catch (error) {
        throw error;
      }
    },

    // Auction Item Image Management
    async getAuctionItemImages(auctionItemId: number): Promise<{
      images: {
        id: number;
        url: string;
        thumbnail_url: string;
        original_name: string;
        file_size: number | null;
        created_at: string;
      }[];
    }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{
          images: {
            id: number;
            url: string;
            thumbnail_url: string;
            original_name: string;
            file_size: number | null;
            created_at: string;
          }[];
        }>(
          `/auction-items/${auctionItemId}/images`,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async uploadAuctionItemImages(
      auctionItemId: number,
      images: {
        url: string;
        original_name: string;
        size: number;
      }[]
    ): Promise<{ message: string; images: any[] }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{
          message: string;
          images: any[];
        }>(
          `/images/auction-items`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify({
              auction_item_id: auctionItemId,
              images: images,
            }),
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async deleteAuctionItemImage(
      imageId: number
    ): Promise<{ message: string }> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<{ message: string }>(
          `/images/${imageId}`,
          {
            method: "DELETE",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    // Categories
    async getCategories(
      filters?: CategoryFilters
    ): Promise<PaginatedResponse<CategoryModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const params = new URLSearchParams();
        if (filters?.search) params.append("search", filters.search);
        if (filters?.has_auctions !== undefined)
          params.append("has_auctions", filters.has_auctions.toString());
        if (filters?.per_page)
          params.append("per_page", filters.per_page.toString());
        if (filters?.page) params.append("page", filters.page.toString());

        const queryString = params.toString();
        // const url = `/categories${queryString ? `?${queryString}` : ""}`;
        const url = `/categories`;

        const response = await GlobalFetchJson<
          PaginatedResponse<CategoryModel>
        >(
          url,
          {
            method: "GET",
            headers: headers,
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    async createCategory(name: string): Promise<SingleResponse<CategoryModel>> {
      try {
        const headers = new Headers();
        headers?.set("Content-Type", "application/json");

        const response = await GlobalFetchJson<SingleResponse<CategoryModel>>(
          `/categories`,
          {
            method: "POST",
            headers: headers,
            body: JSON.stringify({ name }),
          },
          "/api/proxy"
        );
        return response;
      } catch (error) {
        throw error;
      }
    },

    // Image Management Methods
    async uploadImages(images: File[]): Promise<{
      message: string;
      images: Array<{
        url: string;
        thumbnail_url: string;
        original_name: string;
        path: string;
      }>;
      upload_errors: string[];
    }> {
      try {
        // Import Vercel Blob service dynamically to avoid SSR issues
        const { vercelBlobService } = await import(
          "@/services/VercelBlobService"
        );

        if (!vercelBlobService.isConfigured()) {
          throw new Error("Vercel Blob Storage is not configured");
        }

        // Generate directory for this upload session
        const directory = `temp/${Date.now()}`;

        // Upload images to Vercel Blob
        const uploadResult = await vercelBlobService.uploadFiles(
          images,
          directory
        );

        if (uploadResult.failed.length > 0) {
          console.warn("Some uploads failed:", uploadResult.failed);
        }

        // Send URLs to backend for processing
        const imageData = uploadResult.successful.map((result, index) => ({
          url: result.url,
          original_name: images[index]?.name || "unknown",
          size: result.size,
        }));

        if (imageData.length > 0) {
          // Send to backend to validate and process
          const backendResponse = await GlobalFetchJson<{
            message: string;
            images: Array<{
              url: string;
              thumbnail_url: string;
              original_name: string;
              path: string;
            }>;
            upload_errors: string[];
          }>(
            `/images/upload`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ images: imageData }),
            },
            "/api/proxy"
          );

          return backendResponse;
        }

        return {
          message: "No images to upload",
          images: [],
          upload_errors: uploadResult.failed.map((f) => f.error),
        };
      } catch (error) {
        throw error;
      }
    },
  };
}
