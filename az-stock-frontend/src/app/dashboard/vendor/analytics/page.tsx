"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import StatsCard from "@/modules/dashboard/components/shared/StatsCard";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import VendorService from "@/modules/dashboard/services/VendorService";

const vendorService = new VendorService();
import {
  AuctionModel,
  BidModel,
} from "@/modules/dashboard/models/DashboardModels";

interface VendorAnalytics {
  totalProducts: number;
  totalAuctions: number;
  activeAuctions: number;
  totalBids: number;
  totalRevenue: string;
  averageBidAmount: string;
  topPerformingProducts: Array<{
    product_name: string;
    auction_count: number;
    total_bids: number;
    highest_bid: string;
  }>;
  recentActivity: Array<{
    type: string;
    description: string;
    created_at: string;
  }>;
}

const VendorAnalyticsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [analytics, setAnalytics] = useState<VendorAnalytics | null>(null);
  const [recentAuctions, setRecentAuctions] = useState<AuctionModel[]>([]);
  const [recentBids, setRecentBids] = useState<BidModel[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("vendor")) {
      router.push("/");
      return;
    }

    loadAnalytics();
  }, [session, status, router]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      const [auctionsResponse, analyticsResponse] = await Promise.all([
        vendorService.methods.getMyAuctions({ per_page: 10 }),
        vendorService.methods.getAnalytics(),
      ]);

      setRecentAuctions(auctionsResponse.data);
      setAnalytics(analyticsResponse);

      // Extract recent bids from auctions
      const allBids = auctionsResponse.data.flatMap(
        (auction) => auction.bids || []
      );
      const sortedBids = allBids.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
      setRecentBids(sortedBids.slice(0, 10));
    } catch (error) {
      console.error("Error loading analytics:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: "bg-green-100 text-green-800",
      ended: "bg-gray-100 text-gray-800",
      pending: "bg-yellow-100 text-yellow-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusClasses[status as keyof typeof statusClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status}
      </span>
    );
  };

  const auctionColumns = [
    {
      key: "product",
      header: "Product",
      render: (auction: AuctionModel) => (
        <div className="flex items-center">
          {auction.product.image && (
            <img
              src={auction.product.image}
              alt={auction.product.name}
              className="h-8 w-8 rounded-lg object-cover mr-2"
            />
          )}
          <span className="font-medium text-gray-900">
            {auction.product.name}
          </span>
        </div>
      ),
    },
    {
      key: "current_price",
      header: "Current Price",
      render: (auction: AuctionModel) => (
        <span className="font-medium text-green-600">
          ${parseFloat(auction.current_price).toFixed(2)}
        </span>
      ),
    },
    {
      key: "bids_count",
      header: "Bids",
      render: (auction: AuctionModel) => auction.bids?.length || 0,
    },
    {
      key: "status",
      header: "Status",
      render: (auction: AuctionModel) => getStatusBadge(auction.status),
    },
  ];

  const bidColumns = [
    {
      key: "bidder",
      header: "Bidder",
      render: (bid: BidModel) => bid.user.name,
    },
    {
      key: "bid_amount",
      header: "Amount",
      render: (bid: BidModel) => (
        <span className="font-medium text-green-600">
          ${parseFloat(bid.bid_amount).toFixed(2)}
        </span>
      ),
    },
    {
      key: "auction",
      header: "Auction",
      render: (bid: BidModel) => bid.auction?.product.name || "Unknown",
    },
    {
      key: "created_at",
      header: "Date",
      render: (bid: BidModel) => new Date(bid.created_at).toLocaleDateString(),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("vendor")) {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="vendor">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
          <p className="mt-1 text-sm text-gray-500">
            Track your performance and sales metrics
          </p>
        </div>

        {/* Key Metrics */}
        {analytics && (
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              title="Total Products"
              value={analytics.totalProducts}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                  />
                </svg>
              }
            />
            <StatsCard
              title="Total Auctions"
              value={analytics.totalAuctions}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  />
                </svg>
              }
            />
            <StatsCard
              title="Active Auctions"
              value={analytics.activeAuctions}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              }
            />
            <StatsCard
              title="Total Revenue"
              value={`$${parseFloat(analytics.totalRevenue).toFixed(2)}`}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              }
            />
          </div>
        )}

        {/* Performance Metrics */}
        {analytics && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Bidding Statistics
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500">
                    Total Bids Received
                  </span>
                  <span className="text-lg font-semibold text-gray-900">
                    {analytics.totalBids}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500">
                    Average Bid Amount
                  </span>
                  <span className="text-lg font-semibold text-green-600">
                    ${parseFloat(analytics.averageBidAmount).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500">
                    Bids per Auction
                  </span>
                  <span className="text-lg font-semibold text-blue-600">
                    {analytics.totalAuctions > 0
                      ? (analytics.totalBids / analytics.totalAuctions).toFixed(
                          1
                        )
                      : "0"}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Top Performing Products
              </h3>
              <div className="space-y-3">
                {analytics.topPerformingProducts
                  .slice(0, 5)
                  .map((product, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center"
                    >
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {product.product_name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {product.total_bids} bids
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-semibold text-green-600">
                          ${parseFloat(product.highest_bid).toFixed(2)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {product.auction_count} auctions
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Recent Auctions
              </h3>
              <DataTable
                data={recentAuctions}
                columns={auctionColumns}
                loading={loading}
                emptyMessage="No recent auctions."
                showPagination={false}
              />
            </div>
          </div>

          <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Recent Bids
              </h3>
              <DataTable
                data={recentBids}
                columns={bidColumns}
                loading={loading}
                emptyMessage="No recent bids."
                showPagination={false}
              />
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => router.push("/dashboard/vendor/products")}
              className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              Add Product
            </button>
            <button
              onClick={() => router.push("/dashboard/vendor/auctions")}
              className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
              Create Auction
            </button>
            <button
              onClick={() => window.location.reload()}
              className="flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              <svg
                className="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Refresh Data
            </button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default VendorAnalyticsPage;
