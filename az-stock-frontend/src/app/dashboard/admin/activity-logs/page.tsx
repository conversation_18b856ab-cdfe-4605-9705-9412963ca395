"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import StatsCard from "@/modules/dashboard/components/shared/StatsCard";
import { adminService } from "@/modules/dashboard/services/AdminService";
import {
  ActivityLogModel,
  ActivityLogFilters,
  ActivityLogStats,
  PaginationMeta,
} from "@/modules/dashboard/models/DashboardModels";

const AdminActivityLogsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [logs, setLogs] = useState<ActivityLogModel[]>([]);
  const [stats, setStats] = useState<ActivityLogStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<PaginationMeta | null>(null);
  const [filters, setFilters] = useState<ActivityLogFilters>({
    per_page: 15,
    page: 1,
  });
  useEffect(() => {
    if (status === "loading") return;

    if (!session || (session.user as any)?.role !== "admin") {
      router.push("/");
      return;
    }

    loadActivityLogs();
  }, [session, status, router, filters]);

  const loadActivityLogs = async () => {
    try {
      setLoading(true);
      const [logsResponse, statsResponse] = await Promise.all([
        adminService.getActivityLogs(filters),
        adminService.getActivityLogStats(),
      ]);
      setLogs(logsResponse.data);
      setPagination(logsResponse.meta || null);
      setStats(statsResponse);
    } catch (error) {
      console.error("Error loading activity logs:", error);
    } finally {
      setLoading(false);
    }
  };

  const getActionTypeBadge = (actionType: string) => {
    const typeClasses = {
      auth: "bg-blue-100 text-blue-800",
      crud: "bg-green-100 text-green-800",
      system: "bg-purple-100 text-purple-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          typeClasses[actionType as keyof typeof typeClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {actionType}
      </span>
    );
  };

  const getEntityTypeBadge = (entityType: string) => {
    const entityClasses = {
      user: "bg-indigo-100 text-indigo-800",
      product: "bg-yellow-100 text-yellow-800",
      auction: "bg-red-100 text-red-800",
      bid: "bg-green-100 text-green-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          entityClasses[entityType as keyof typeof entityClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {entityType}
      </span>
    );
  };

  const columns = [
    {
      key: "id",
      header: "ID",
      sortable: true,
    },
    {
      key: "user",
      header: "User",
      render: (log: ActivityLogModel) => (
        <div>
          <div className="font-medium text-gray-900">
            {log.user?.name || "Unknown User"}
          </div>
          <div className="text-sm text-gray-500">ID: {log.user_id}</div>
        </div>
      ),
    },
    {
      key: "action_type",
      header: "Action Type",
      render: (log: ActivityLogModel) => getActionTypeBadge(log.action_type),
    },
    {
      key: "entity_type",
      header: "Entity Type",
      render: (log: ActivityLogModel) => getEntityTypeBadge(log.entity_type),
    },
    {
      key: "entity_id",
      header: "Entity ID",
      render: (log: ActivityLogModel) => log.entity_id || "-",
    },
    {
      key: "description",
      header: "Description",
      render: (log: ActivityLogModel) => (
        <div className="max-w-xs truncate" title={log.description}>
          {log.description}
        </div>
      ),
    },
    {
      key: "ip_address",
      header: "IP Address",
    },
    {
      key: "created_at",
      header: "Date",
      render: (log: ActivityLogModel) => (
        <div>
          <div className="text-sm text-gray-900">
            {new Date(log.created_at).toLocaleDateString()}
          </div>
          <div className="text-xs text-gray-500">
            {new Date(log.created_at).toLocaleTimeString()}
          </div>
        </div>
      ),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if ((session.user as any)?.role !== "admin") {
    return <div>Access denied</div>;
  }

  return (
    <DashboardLayout role="admin">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Activity Logs</h1>
          <p className="mt-1 text-sm text-gray-500">
            Monitor system activity and user actions
          </p>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              title="Total Logs"
              value={stats.total_logs}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              }
            />
            <StatsCard
              title="Auth Actions"
              value={stats.logs_by_action_type.auth?.count || 0}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              }
            />
            <StatsCard
              title="CRUD Actions"
              value={stats.logs_by_action_type.crud?.count || 0}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  />
                </svg>
              }
            />
            <StatsCard
              title="System Actions"
              value={stats.logs_by_action_type.system?.count || 0}
              loading={loading}
              icon={
                <svg
                  className="w-8 h-8"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              }
            />
          </div>
        )}

        {/* Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                User ID
              </label>
              <input
                type="number"
                value={filters.user_id || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    user_id: e.target.value
                      ? parseInt(e.target.value)
                      : undefined,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="Filter by user ID"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Action Type
              </label>
              <select
                value={filters.action_type || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    action_type: e.target.value as any,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Actions</option>
                <option value="auth">Authentication</option>
                <option value="crud">CRUD Operations</option>
                <option value="system">System</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Entity Type
              </label>
              <select
                value={filters.entity_type || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    entity_type: e.target.value as any,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Entities</option>
                <option value="user">User</option>
                <option value="product">Product</option>
                <option value="auction">Auction</option>
                <option value="bid">Bid</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Start Date
              </label>
              <input
                type="date"
                value={filters.start_date || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    start_date: e.target.value,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                End Date
              </label>
              <input
                type="date"
                value={filters.end_date || ""}
                onChange={(e) =>
                  setFilters({ ...filters, end_date: e.target.value, page: 1 })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              />
            </div>
          </div>
        </div>

        {/* Activity Logs Table */}
        <DataTable
          data={logs}
          columns={columns}
          loading={loading}
          pagination={pagination || undefined}
          onPageChange={(page) => setFilters({ ...filters, page })}
          emptyMessage="No activity logs found for the selected filters."
        />

        {/* Activity Statistics */}
        {stats && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Entity Type Distribution */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Activity by Entity Type
              </h3>
              <div className="space-y-3">
                {Object.entries(stats.logs_by_entity_type).map(
                  ([type, data]) => (
                    <div
                      key={type}
                      className="flex justify-between items-center"
                    >
                      <div className="flex items-center">
                        {getEntityTypeBadge(type)}
                        <span className="ml-2 text-sm font-medium text-gray-900 capitalize">
                          {type}
                        </span>
                      </div>
                      <span className="text-sm text-gray-900">
                        {data.count}
                      </span>
                    </div>
                  )
                )}
              </div>
            </div>

            {/* Top Active Users */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Most Active Users
              </h3>
              <div className="space-y-3">
                {stats.top_users.slice(0, 10).map((user) => (
                  <div
                    key={user.user_id}
                    className="flex justify-between items-center"
                  >
                    <div>
                      <span className="text-sm font-medium text-gray-900">
                        {user.user_name}
                      </span>
                      <span className="ml-2 text-xs text-gray-500">
                        ID: {user.user_id}
                      </span>
                    </div>
                    <span className="text-sm text-gray-500">
                      {user.activity_count} actions
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default AdminActivityLogsPage;
