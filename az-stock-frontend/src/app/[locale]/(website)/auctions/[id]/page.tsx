import Loading from "@/app/[locale]/loading";
import useGlobalService from "@/core/hook/useGlobalService";
import AuctionDetails from "@/modules/auctions/components/AuctionDetails";
import AuctionService from "@/modules/auctions/services/AuctionService";
import Container from "@/modules/common/components/Container";
import { Metadata } from "next";
import { Suspense } from "react";

const auctionService = useGlobalService(AuctionService);

export async function generateMetadata({
  params,
}: {
  params: { id: string };
}): Promise<Metadata> {
  try {
    const data = await auctionService.methods.getAuction({ id: params.id });

    return {
      metadataBase: new URL(process.env.NEXT_PUBLIC_URL!),
      title: `${data?.title} - Auction`,
      description: `${data?.description || "No description available"}`,
      openGraph: {
        url: `${process.env.NEXT_PUBLIC_URL!}/auctions/${data?.id}`,
        description: `${data?.description || "No description available"}`,
        title: `${data?.title} - Auction`,
      },
      alternates: {
        canonical: `/auctions/${data?.id}`,
      },
    };
  } catch (error) {
    console.error("Error generating metadata for auction:", error);
    return {
      title: "Auction - AZStock",
      description: "Auction details",
    };
  }
}

const Auction = async ({ params }: { params: { id: string } }) => {
  try {
    const data = await auctionService.methods.getAuction({ id: params.id });
    console.log(data);
    return <AuctionDetails data={data} />;
  } catch (error) {
    console.error(`Error fetching auction with ID ${params.id}:`, error);
    return (
      <Container className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-12">
          <h3 className="text-xl font-medium text-red-600">
            Error loading auction
          </h3>
          <p className="mt-2 text-gray-500">
            There was a problem loading this auction. Please try again later.
          </p>
        </div>
      </Container>
    );
  }
};

const Page = ({ params }: { params: { id: string } }) => {
  return (
    <Suspense fallback={<Loading />}>
      <Auction params={params} />
    </Suspense>
  );
};

export default Page;
