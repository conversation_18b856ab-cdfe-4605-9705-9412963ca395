import useGlobalService from "@/core/hook/useGlobalService";
import { Suspense } from "react";
import Loading from "../loading";
import CategoryService from "@/modules/common/services/CategoryService";
import Home from "@/modules/home/<USER>/Home";
import AuctionService from "@/modules/auctions/services/AuctionService";

async function Main({ searchParams }: { searchParams: { category: string } }) {
  const auctionService = useGlobalService(AuctionService);
  const categoryService = useGlobalService(CategoryService);

  try {
    const auctions = await auctionService.methods.getAuctions({
      per_page: 8,
      category_id: searchParams.category
        ? parseInt(searchParams.category)
        : undefined,
      status: "active",
    });
    const categories = await categoryService.methods.getCategories({});

    return <Home auctions={auctions} categories={categories} />;
  } catch (error) {
    console.error("Error loading home page data:", error);
    return <Home auctions={[]} categories={[]} />;
  }
}

export default async function Page({
  searchParams,
}: {
  searchParams: { category: string };
}) {
  return (
    <Suspense fallback={<Loading />}>
      <Main searchParams={searchParams} />
    </Suspense>
  );
}
