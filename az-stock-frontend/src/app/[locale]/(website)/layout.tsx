import Footer from "@/modules/common/components/Footer/Footer";
import Navbar from "@/modules/common/components/Navbar/Navbar";
import { Metadata } from "next";

import { notFound } from "next/navigation";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  try {
  } catch (error) {
    return notFound();
  }
  return (
    <>
      <Navbar />
      <div className="min-h-[86vh] w-full">{children}</div>
      <Footer />
    </>
  );
}
