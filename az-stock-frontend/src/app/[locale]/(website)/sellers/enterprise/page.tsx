import Container from "@/modules/common/components/Container";

const Page = () => {
  return (
    <div className=" flex flex-col items-center overflow-hidden">
      <Container className="pb-20 flex flex-col justify-center items-center gap-2 h-[80vh] text-center !max-w-5xl">
        <div>
          <h1 className="text-4xl text-gray-900 font-bold">
            What’s Your Excess Inventory Costing You?
          </h1>
          <p className="font-bold text-lg pt-4">
            Each year, a typical large brand or retailer holds over $1B in
            returned, excess, and slow-moving inventory—and wastes precious
            time, money, and resources struggling to effectively handle it all.
            Hanging onto these goods ties up your cash and space as the
            inventory's value degrades with time. Remarketing it all can be
            incredibly expensive. Moving it to a landfill gains you little but
            bad press. And traditional liquidation options are unreliable,
            unpredictable, and difficult to manage.
          </p>
        </div>
      </Container>
    </div>
  );
};
export default Page;
