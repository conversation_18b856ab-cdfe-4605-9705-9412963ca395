"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import StatsCard from "@/modules/dashboard/components/shared/StatsCard";
import Modal from "@/modules/dashboard/components/shared/Modal";
import { adminService } from "@/modules/dashboard/services/AdminService";
import {
  UserModel,
  UserFilters,
  UserVerificationStatus,
  VerifyUserForm,
} from "@/modules/dashboard/models/DashboardModels";

const AdminUserVerificationPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [users, setUsers] = useState<UserModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);
  const [filters, setFilters] = useState<UserFilters>({
    per_page: 15,
    page: 1,
  });
  const [selectedUser, setSelectedUser] = useState<UserModel | null>(null);
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationStatus, setVerificationStatus] =
    useState<UserVerificationStatus | null>(null);
  const [verificationLoading, setVerificationLoading] = useState(false);

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("admin")) {
      router.push("/");
      return;
    }

    loadUsers();
  }, [session, status, router, filters]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await adminService.getUsers(filters);
      setUsers(response.data);
      setPagination(response.meta);
    } catch (error) {
      console.error("Error loading users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationClick = async (user: UserModel) => {
    try {
      setSelectedUser(user);
      setVerificationLoading(true);
      const status = await adminService.getUserVerificationStatus(user.id);
      console.log(status);
      setVerificationStatus(status);
      setShowVerificationModal(true);
    } catch (error) {
      console.error("Error loading verification status:", error);
    } finally {
      setVerificationLoading(false);
    }
  };

  const handleVerifyUser = async (
    role: "buyer" | "vendor" | "admin",
    verified: boolean
  ) => {
    if (!selectedUser) return;

    try {
      setVerificationLoading(true);
      const verificationData: VerifyUserForm = { role, verified };
      await adminService.verifyUser(selectedUser.id, verificationData);

      // Reload verification status
      const updatedStatus = await adminService.getUserVerificationStatus(
        selectedUser.id
      );
      setVerificationStatus(updatedStatus);

      // Reload users list
      await loadUsers();
    } catch (error) {
      console.error("Error updating verification:", error);
    } finally {
      setVerificationLoading(false);
    }
  };

  const getVerificationBadge = (
    user: UserModel,
    role: "buyer" | "vendor" | "admin"
  ) => {
    const isVerified = user.verification_status?.[role];
    const hasRole = user.roles?.includes(role);

    if (!hasRole) {
      return <span className="text-xs text-gray-400">N/A</span>;
    }

    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          isVerified ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
        }`}
      >
        {isVerified ? "Verified" : "Unverified"}
      </span>
    );
  };

  const columns = [
    {
      key: "id",
      header: "ID",
      sortable: true,
    },
    {
      key: "name",
      header: "Name",
      render: (user: UserModel) => (
        <div>
          <div className="font-medium text-gray-900">{user.name}</div>
          <div className="text-sm text-gray-500">{user.email}</div>
        </div>
      ),
    },
    {
      key: "roles",
      header: "Roles",
      render: (user: UserModel) => (
        <div className="flex flex-wrap gap-1">
          {user.roles?.map((role) => (
            <span
              key={role}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {role}
            </span>
          ))}
        </div>
      ),
    },
    {
      key: "buyer_verification",
      header: "Buyer",
      render: (user: UserModel) => getVerificationBadge(user, "buyer"),
    },
    {
      key: "vendor_verification",
      header: "Vendor",
      render: (user: UserModel) => getVerificationBadge(user, "vendor"),
    },
    {
      key: "admin_verification",
      header: "Admin",
      render: (user: UserModel) => getVerificationBadge(user, "admin"),
    },
    {
      key: "created_at",
      header: "Registered",
      render: (user: UserModel) => (
        <div className="text-sm text-gray-900">
          {new Date(user.created_at).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: "actions",
      header: "Actions",
      render: (user: UserModel) => (
        <button
          onClick={() => handleVerificationClick(user)}
          className="text-blue-600 hover:text-blue-900 text-sm font-medium"
          disabled={verificationLoading}
        >
          Manage Verification
        </button>
      ),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("admin")) {
    return <div>Access denied</div>;
  }

  return (
    <div className="space-y-6">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            User Verification
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage user verification status for different roles
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Role Filter
              </label>
              <select
                value={filters.role || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    role: e.target.value as any,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Roles</option>
                <option value="buyer">Buyer</option>
                <option value="vendor">Vendor</option>
                <option value="admin">Admin</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Search Users
              </label>
              <input
                type="text"
                value={filters.search || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    search: e.target.value,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                placeholder="Search by name or email"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Active Status
              </label>
              <select
                value={filters.is_active?.toString() || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    is_active: e.target.value
                      ? e.target.value === "true"
                      : undefined,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Users</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>
          </div>
        </div>

        {/* Users Table */}
        <DataTable
          data={users}
          columns={columns}
          loading={loading}
          pagination={pagination}
          onPageChange={(page) => setFilters({ ...filters, page })}
          emptyMessage="No users found for the selected filters."
        />

        {/* Verification Modal */}
        <Modal
          isOpen={showVerificationModal}
          onClose={() => {
            setShowVerificationModal(false);
            setSelectedUser(null);
            setVerificationStatus(null);
          }}
          title={`Manage Verification - ${selectedUser?.name || ""}`}
          size="lg"
        >
          {verificationStatus && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Current Verification Status
                </h3>
                <div className="space-y-4">
                  {verificationStatus?.roles?.map((role) => (
                    <div
                      key={role}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div>
                        <h4 className="font-medium text-gray-900 capitalize">
                          {role}
                        </h4>
                        <p className="text-sm text-gray-500">
                          {role === "buyer" &&
                            "Can place bids and make purchases"}
                          {role === "vendor" &&
                            "Can create products and auctions"}
                          {role === "admin" &&
                            "Can manage all platform resources"}
                        </p>
                      </div>
                      <div className="flex items-center gap-3">
                        <span
                          className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                            verificationStatus.verification_status[
                              role as keyof typeof verificationStatus.verification_status
                            ]
                              ? "bg-green-100 text-green-800"
                              : "bg-red-100 text-red-800"
                          }`}
                        >
                          {verificationStatus.verification_status[
                            role as keyof typeof verificationStatus.verification_status
                          ]
                            ? "Verified"
                            : "Unverified"}
                        </span>
                        <button
                          onClick={() =>
                            handleVerifyUser(
                              role as "buyer" | "vendor" | "admin",
                              !verificationStatus.verification_status[
                                role as keyof typeof verificationStatus.verification_status
                              ]
                            )
                          }
                          disabled={verificationLoading}
                          className={`px-4 py-2 text-sm font-medium rounded-md ${
                            verificationStatus.verification_status[
                              role as keyof typeof verificationStatus.verification_status
                            ]
                              ? "bg-red-600 text-white hover:bg-red-700"
                              : "bg-green-600 text-white hover:bg-green-700"
                          } disabled:opacity-50`}
                        >
                          {verificationLoading
                            ? "..."
                            : verificationStatus.verification_status[
                                role as keyof typeof verificationStatus.verification_status
                              ]
                            ? "Unverify"
                            : "Verify"}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </div>
  );
};

export default AdminUserVerificationPage;
