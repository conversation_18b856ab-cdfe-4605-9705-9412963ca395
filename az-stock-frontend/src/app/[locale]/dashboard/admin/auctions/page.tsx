"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import Modal from "@/modules/dashboard/components/shared/Modal";
import { adminService } from "@/modules/dashboard/services/AdminService";
import { AuctionModel } from "@/modules/dashboard/models/DashboardModels";

const AdminAuctionsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [auctions, setAuctions] = useState<AuctionModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);
  const [filters, setFilters] = useState({
    status: undefined as string | undefined,
    per_page: 15,
    page: 1,
  });
  const [showEndModal, setShowEndModal] = useState(false);
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [selectedAuction, setSelectedAuction] = useState<AuctionModel | null>(
    null
  );

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("admin")) {
      router.push("/");
      return;
    }

    loadAuctions();
  }, [session, status, router, filters]);

  const loadAuctions = async () => {
    try {
      setLoading(true);
      const response = await adminService.getAllAuctions(filters);
      setAuctions(response.data);
      setPagination(response.meta);
    } catch (error) {
      console.error("Error loading auctions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleEndAuction = async () => {
    if (!selectedAuction) return;

    try {
      await adminService.endAuction(selectedAuction.id);
      setShowEndModal(false);
      setSelectedAuction(null);
      loadAuctions();
    } catch (error) {
      console.error("Error ending auction:", error);
      alert("Failed to end auction. Please try again.");
    }
  };

  const handleProcessRefunds = async () => {
    if (!selectedAuction) return;

    try {
      await adminService.processRefunds(selectedAuction.id);
      setShowRefundModal(false);
      setSelectedAuction(null);
      loadAuctions();
      alert("Refunds processed successfully!");
    } catch (error) {
      console.error("Error processing refunds:", error);
      alert("Failed to process refunds. Please try again.");
    }
  };

  const openEndModal = (auction: AuctionModel) => {
    setSelectedAuction(auction);
    setShowEndModal(true);
  };

  const openRefundModal = (auction: AuctionModel) => {
    setSelectedAuction(auction);
    setShowRefundModal(true);
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: "bg-green-100 text-green-800",
      completed: "bg-gray-100 text-gray-800",
      draft: "bg-yellow-100 text-yellow-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusClasses[status as keyof typeof statusClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status}
      </span>
    );
  };

  const getTimeRemaining = (endTime: string) => {
    const end = new Date(endTime);
    const now = new Date();
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) return "Ended";

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const columns = [
    {
      key: "id",
      header: "ID",
      sortable: true,
    },
    {
      key: "product",
      header: "Product",
      render: (auction: AuctionModel) => (
        <div className="flex items-center">
          {auction.product?.image && (
            <img
              src={auction.product.image}
              alt={auction.product.name}
              className="h-10 w-10 rounded-lg object-cover mr-3"
            />
          )}
          <div>
            <div className="font-medium text-gray-900">
              {auction.product?.name || auction.title || "Untitled Auction"}
            </div>
            <div className="text-sm text-gray-500">
              by {auction.vendor?.name || "Unknown Vendor"}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: "current_price",
      header: "Current Price",
      render: (auction: AuctionModel) => (
        <span className="font-medium text-green-600">
          ${parseFloat(auction.current_price).toFixed(2)}
        </span>
      ),
    },
    {
      key: "starting_price",
      header: "Starting Price",
      render: (auction: AuctionModel) =>
        `$${parseFloat(auction.starting_price).toFixed(2)}`,
    },
    {
      key: "reserve_price",
      header: "Reserve Price",
      render: (auction: AuctionModel) =>
        auction.reserve_price
          ? `$${parseFloat(auction.reserve_price).toFixed(2)}`
          : "N/A",
    },
    {
      key: "bids_count",
      header: "Bids",
      render: (auction: AuctionModel) => auction.bids?.length || 0,
    },
    {
      key: "time_remaining",
      header: "Time Remaining",
      render: (auction: AuctionModel) => (
        <span
          className={`font-medium ${
            getTimeRemaining(auction.end_time) === "Ended"
              ? "text-red-600"
              : "text-blue-600"
          }`}
        >
          {getTimeRemaining(auction.end_time)}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (auction: AuctionModel) => getStatusBadge(auction.status),
    },
    {
      key: "created_at",
      header: "Created",
      render: (auction: AuctionModel) =>
        new Date(auction.created_at).toLocaleDateString(),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("admin")) {
    return <div>Access denied</div>;
  }

  return (
    <div className="space-y-6">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Auction Management
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Oversee all auctions in the system
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                value={filters.status || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    status: e.target.value || undefined,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="draft">Draft</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Per Page
              </label>
              <select
                value={filters.per_page}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    per_page: parseInt(e.target.value),
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value={10}>10</option>
                <option value={15}>15</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>

        {/* Auctions Table */}
        <DataTable
          data={auctions}
          columns={columns}
          loading={loading}
          pagination={pagination}
          onPageChange={(page) => setFilters({ ...filters, page })}
          actions={(auction) => (
            <div className="flex space-x-2">
              <button
                onClick={() => router.push(`/auctions/${auction.id}`)}
                className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
              >
                View
              </button>
              {auction.status === "active" && (
                <button
                  onClick={() => openEndModal(auction)}
                  className="text-red-600 hover:text-red-900 text-sm font-medium"
                >
                  End
                </button>
              )}
              {auction.status === "completed" &&
                auction.bids &&
                auction.bids.length > 0 && (
                  <button
                    onClick={() => openRefundModal(auction)}
                    className="text-blue-600 hover:text-blue-900 text-sm font-medium"
                  >
                    Refunds
                  </button>
                )}
            </div>
          )}
          emptyMessage="No auctions found."
        />

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {
                  auctions.filter((auction) => auction.status === "active")
                    .length
                }
              </div>
              <div className="text-sm text-gray-500">Active Auctions</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {
                  auctions.filter((auction) => auction.status === "completed")
                    .length
                }
              </div>
              <div className="text-sm text-gray-500">Completed Auctions</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {auctions.reduce(
                  (total, auction) => total + (auction.bids?.length || 0),
                  0
                )}
              </div>
              <div className="text-sm text-gray-500">Total Bids</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                $
                {auctions
                  .reduce(
                    (total, auction) =>
                      total + parseFloat(auction.current_price),
                    0
                  )
                  .toFixed(2)}
              </div>
              <div className="text-sm text-gray-500">Total Value</div>
            </div>
          </div>
        </div>

        {/* End Auction Modal */}
        <Modal
          isOpen={showEndModal}
          onClose={() => setShowEndModal(false)}
          title="End Auction"
        >
          {selectedAuction && (
            <div className="space-y-4">
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex">
                  <svg
                    className="h-5 w-5 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">
                      Confirm Auction End
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>Are you sure you want to end this auction?</p>
                      <p className="mt-2 font-medium">
                        Auction:{" "}
                        {selectedAuction.product?.name ||
                          selectedAuction.title ||
                          "Untitled Auction"}
                      </p>
                      <p>
                        Current Price: $
                        {parseFloat(selectedAuction.current_price).toFixed(2)}
                      </p>
                      <p>Total Bids: {selectedAuction.bids?.length || 0}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowEndModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleEndAuction}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  End Auction
                </button>
              </div>
            </div>
          )}
        </Modal>

        {/* Process Refunds Modal */}
        <Modal
          isOpen={showRefundModal}
          onClose={() => setShowRefundModal(false)}
          title="Process Refunds"
        >
          {selectedAuction && (
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex">
                  <svg
                    className="h-5 w-5 text-blue-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">
                      Process Refunds
                    </h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>
                        This will process refunds for all losing bidders in this
                        auction.
                      </p>
                      <p className="mt-2 font-medium">
                        Auction:{" "}
                        {selectedAuction.product?.name ||
                          selectedAuction.title ||
                          "Untitled Auction"}
                      </p>
                      <p>Total Bids: {selectedAuction.bids?.length || 0}</p>
                      <p>
                        Winning Bid: $
                        {parseFloat(selectedAuction.current_price).toFixed(2)}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowRefundModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleProcessRefunds}
                  className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                >
                  Process Refunds
                </button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </div>
  );
};

export default AdminAuctionsPage;
