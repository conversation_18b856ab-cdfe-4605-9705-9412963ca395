"use client";

import { usePathname } from "next/navigation";
import DashboardLayout from "@/modules/dashboard/components/DashboardLayout";

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  // Extract role from the pathname
  // Expected patterns: /dashboard/admin/*, /dashboard/vendor/*, /dashboard/buyer/*
  const pathSegments = pathname?.split("/") || [];
  const role = pathSegments[2] as "admin" | "vendor" | "buyer" | undefined;

  // If we're on a role-specific path, use DashboardLayout
  if (role && ["admin", "vendor", "buyer"].includes(role)) {
    return <DashboardLayout role={role}>{children}</DashboardLayout>;
  }

  // For general dashboard pages (like /dashboard/profile), use DashboardLayout without specific role
  return <DashboardLayout>{children}</DashboardLayout>;
}
