"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import VendorProfileForm from "@/modules/dashboard/components/forms/VendorProfileForm";
import VendorProfileService from "@/modules/dashboard/services/VendorProfileService";
import { VendorModel } from "@/modules/dashboard/models/DashboardModels";
import { UpdateVendorForm } from "@/modules/dashboard/services/VendorProfileService";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";

const vendorProfileService = new VendorProfileService();

const VendorProfilePage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [updateLoading, setUpdateLoading] = useState(false);

  // Fetch vendor profile
  const {
    data: vendorResponse,
    isLoading: vendorLoading,
    error: vendorError,
    refetch: refetchVendor,
  } = useQuery({
    queryKey: ["vendorProfile"],
    queryFn: () => vendorProfileService.methods.getMyProfile(),
    enabled: !!session?.user?.roles?.includes("vendor"),
  });

  const vendor = vendorResponse?.data;

  useEffect(() => {
    if (status === "loading") return;

    if (!session) {
      router.push("/auth/login");
      return;
    }

    // Check if user is a vendor
    if (!session.user?.roles?.includes("vendor")) {
      router.push("/dashboard");
      return;
    }

    setLoading(false);
  }, [session, status, router]);

  const handleUpdateProfile = async (data: UpdateVendorForm) => {
    if (!vendor) return;

    try {
      setUpdateLoading(true);
      await vendorProfileService.methods.updateVendor(vendor.id, data);

      // Refresh the profile data
      await refetchVendor();

      // Exit edit mode
      setIsEditing(false);

      // Show success message (you can implement a toast notification here)
      alert("Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile:", error);
      alert("Failed to update profile. Please try again.");
    } finally {
      setUpdateLoading(false);
    }
  };

  if (loading || vendorLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (vendorError) {
    return (
      <div className="space-y-6">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Vendor Profile</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your vendor profile and business information
            </p>
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <div className="text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-red-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                Error Loading Profile
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                There was an error loading your vendor profile. Please try
                again.
              </p>
              <button
                onClick={() => refetchVendor()}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Vendor Profile</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your vendor profile and business information
            </p>
          </div>

          {vendor && !isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
            >
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              Edit Profile
            </button>
          )}
        </div>

        {/* Profile Content */}
        <div className="bg-white shadow rounded-lg">
          {isEditing ? (
            <div className="p-6">
              <VendorProfileForm
                vendor={vendor}
                onSubmit={handleUpdateProfile}
                onCancel={() => setIsEditing(false)}
                loading={updateLoading}
              />
            </div>
          ) : vendor ? (
            <div className="p-6">
              {/* Profile Display */}
              <div className="space-y-6">
                {/* Header with Logo and Basic Info */}
                <div className="flex items-center space-x-6">
                  <div className="flex-shrink-0">
                    {vendor.logo ? (
                      <GlobalImage
                        src={vendor.logo}
                        alt={vendor.name}
                        width={80}
                        height={80}
                        className="h-20 w-20 rounded-full object-cover border-4 border-white shadow-lg"
                      />
                    ) : (
                      <div className="h-20 w-20 rounded-full bg-gray-200 flex items-center justify-center border-4 border-white shadow-lg">
                        <svg
                          className="h-10 w-10 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                          />
                        </svg>
                      </div>
                    )}
                  </div>

                  <div className="flex-1">
                    <h2 className="text-2xl font-bold text-gray-900">
                      {vendor.name}
                    </h2>
                    <p className="text-gray-600">{vendor.email}</p>
                    <div className="flex items-center mt-2">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          vendor.is_active
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {vendor.is_active ? "Active" : "Inactive"}
                      </span>
                      {vendor.member_since && (
                        <span className="ml-4 text-sm text-gray-500">
                          Member since {vendor.member_since}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Contact Information and Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">
                      Contact Information
                    </h3>
                    <div className="space-y-3">
                      {vendor.phone && (
                        <div className="flex items-center space-x-3">
                          <svg
                            className="h-5 w-5 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                            />
                          </svg>
                          <span className="text-gray-700">{vendor.phone}</span>
                        </div>
                      )}

                      {vendor.website && (
                        <div className="flex items-center space-x-3">
                          <svg
                            className="h-5 w-5 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                            />
                          </svg>
                          <a
                            href={vendor.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-indigo-600 hover:text-indigo-500"
                          >
                            {vendor.website}
                          </a>
                        </div>
                      )}

                      {vendor.address && (
                        <div className="flex items-start space-x-3">
                          <svg
                            className="h-5 w-5 text-gray-400 mt-0.5"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                          </svg>
                          <span className="text-gray-700">
                            {vendor.address}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">
                      Statistics
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Total Auctions</span>
                        <span className="font-medium">
                          {vendor.total_auctions_count || 0}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Active Auctions</span>
                        <span className="font-medium">
                          {vendor.active_auctions_count || 0}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">
                          Completed Auctions
                        </span>
                        <span className="font-medium">
                          {vendor.completed_auctions_count || 0}
                        </span>
                      </div>
                      {vendor.average_rating && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Average Rating</span>
                          <span className="font-medium">
                            {vendor.average_rating}/5
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Description */}
                {vendor.description && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">
                      About
                    </h3>
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {vendor.description}
                    </p>
                  </div>
                )}

                {/* Public Profile Link */}
                <div className="border-t border-gray-200 pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        Public Profile
                      </h3>
                      <p className="text-sm text-gray-500">
                        This is how buyers will see your profile
                      </p>
                    </div>
                    <a
                      href={`/vendors/${vendor.slug}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <svg
                        className="w-4 h-4 mr-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                        />
                      </svg>
                      View Public Profile
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="p-6">
              <div className="text-center py-12">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No Vendor Profile Found
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  You don't have a vendor profile yet. Create one to start
                  selling.
                </p>
                <button
                  onClick={() => setIsEditing(true)}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  Create Profile
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VendorProfilePage;
