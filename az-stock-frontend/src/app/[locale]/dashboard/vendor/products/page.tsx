"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import Modal from "@/modules/dashboard/components/shared/Modal";
import CreateAuctionFormComponent from "@/modules/dashboard/components/forms/CreateAuctionForm";
import AuctionService from "@/modules/dashboard/services/AuctionService";

const auctionService = new AuctionService();
import {
  AuctionModel,
  CategoryModel,
  CreateAuctionForm,
  AuctionFilters,
} from "@/modules/dashboard/models/DashboardModels";
import GlobalImage from "@/modules/common/components/GlobalImage/GlobalImage";

const VendorAuctionsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [auctions, setAuctions] = useState<AuctionModel[]>([]);
  const [categories, setCategories] = useState<CategoryModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);
  const [filters, setFilters] = useState<AuctionFilters>({
    status: undefined,
    per_page: 15,
    page: 1,
  });
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAuction, setSelectedAuction] = useState<AuctionModel | null>(
    null
  );
  const [showImageModal, setShowImageModal] = useState(false);
  const queryClient = useQueryClient();

  // Use React Query for auction item images
  const {
    data: auctionImages,
    isLoading: imagesLoading,
    error: imagesError,
  } = useQuery({
    queryKey: ["auctionImages", selectedAuction?.id],
    queryFn: async () => {
      try {
        if (
          !selectedAuction?.auction_items ||
          selectedAuction.auction_items.length === 0
        ) {
          return [];
        }
        // Get images for all auction items
        const allImages = selectedAuction.auction_items.flatMap(
          (item) => item.images || []
        );
        return allImages;
      } catch (error) {
        console.error("Error fetching auction images:", error);
        return []; // Return empty array on error
      }
    },
    enabled: !!selectedAuction?.id && (showEditModal || showImageModal),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
  console.log(auctionImages);

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("vendor")) {
      router.push("/");
      return;
    }

    loadData();
  }, [session, status, router, filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [auctionsResponse, categoriesResponse] = await Promise.all([
        auctionService.methods.getMyAuctions(filters),
        auctionService.methods.getCategories(),
      ]);
      console.log(categoriesResponse);
      setAuctions(auctionsResponse.data);
      setPagination(auctionsResponse.meta);
      setCategories(categoriesResponse.data);
    } catch (error) {
      console.error("Error loading auctions:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAuction = async (auctionData: CreateAuctionForm) => {
    try {
      await auctionService.methods.createAuction(auctionData);
      setShowCreateModal(false);
      loadData();
    } catch (error) {
      console.error("Error creating auction:", error);
      alert("Failed to create auction. Please try again.");
    }
  };

  const handleUpdateAuction = async (auctionData: CreateAuctionForm) => {
    if (!selectedAuction) return;

    try {
      await auctionService.methods.updateAuction(selectedAuction.id, {
        title: auctionData.title,
        description: auctionData.description,
        end_time: auctionData.end_time,
        reserve_price: auctionData.reserve_price,
      });
      setShowEditModal(false);
      setSelectedAuction(null);
      loadData();
    } catch (error) {
      console.error("Error updating auction:", error);
      alert("Failed to update auction. Please try again.");
    }
  };

  const handleDeleteAuction = async (auctionId: number) => {
    if (!confirm("Are you sure you want to delete this auction?")) return;

    try {
      await auctionService.methods.deleteAuction(auctionId);
      loadData();
    } catch (error) {
      console.error("Error deleting auction:", error);
      alert("Failed to delete auction. Please try again.");
    }
  };

  const openEditModal = (auction: AuctionModel) => {
    setSelectedAuction(auction);
    setShowEditModal(true);
  };

  const openImageModal = (auction: AuctionModel) => {
    setSelectedAuction(auction);
    setShowImageModal(true);
  };

  const handleDeleteAuctionImage = async (imageId: number) => {
    if (!confirm("Are you sure you want to delete this image?")) return;

    try {
      await auctionService.methods.deleteAuctionItemImage(imageId);
      // Invalidate and refetch the images query
      await queryClient.invalidateQueries({
        queryKey: ["auctionImages", selectedAuction?.id],
      });
      // Also reload the auctions list to update the table display
      await loadData();
    } catch (error) {
      console.error("Error deleting image:", error);
      alert("Failed to delete image. Please try again.");
    }
  };

  const columns = [
    {
      key: "id",
      header: "ID",
      sortable: true,
    },
    {
      key: "auction",
      header: "Auction",
      render: (auction: AuctionModel) => {
        const firstItem = auction.auction_items?.[0];
        const firstImage = firstItem?.images?.[0];
        const itemCount = auction.auction_items?.length || 0;

        return (
          <div className="flex items-center">
            <div className="relative mr-3">
              {firstImage ? (
                <GlobalImage
                  src={firstImage.url}
                  alt={firstItem?.item_name || auction.title}
                  width={20}
                  height={20}
                  className="h-12 w-12 rounded-lg object-cover"
                />
              ) : (
                <div className="h-12 w-12 rounded-lg bg-gray-200 flex items-center justify-center">
                  <svg
                    className="h-6 w-6 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              )}
              {auction.status === "active" && (
                <div className="absolute -top-1 -right-1 bg-green-600 text-white text-xs px-1 rounded">
                  ●
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="font-medium text-gray-900">{auction.title}</div>
              <div className="text-sm text-gray-500 max-w-xs truncate">
                {auction.description}
              </div>
              {itemCount > 0 && (
                <div className="text-xs text-indigo-600 mt-1">
                  {itemCount} item{itemCount !== 1 ? "s" : ""}
                </div>
              )}
            </div>
          </div>
        );
      },
    },
    {
      key: "category",
      header: "Category",
      render: (auction: AuctionModel) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {auction.category?.name || "Uncategorized"}
        </span>
      ),
    },
    {
      key: "current_price",
      header: "Current Price",
      render: (auction: AuctionModel) => (
        <span className="font-medium text-green-600">
          ${parseFloat(auction.current_price).toFixed(2)}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (auction: AuctionModel) => {
        const statusColors = {
          draft: "bg-gray-100 text-gray-800",
          active: "bg-green-100 text-green-800",
          completed: "bg-blue-100 text-blue-800",
          cancelled: "bg-red-100 text-red-800",
        };
        return (
          <span
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              statusColors[auction.status] || "bg-gray-100 text-gray-800"
            }`}
          >
            {auction.status}
          </span>
        );
      },
    },
    {
      key: "end_time",
      header: "End Time",
      render: (auction: AuctionModel) =>
        new Date(auction.end_time).toLocaleDateString(),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("vendor")) {
    return <div>Access denied</div>;
  }

  return (
    <div className="space-y-6">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Auctions</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your auction listings
            </p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Create Auction
          </button>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                value={filters.status || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    status: (e.target.value as any) || undefined,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Auctions</option>
                <option value="draft">Draft</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Per Page
              </label>
              <select
                value={filters.per_page}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    per_page: parseInt(e.target.value),
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value={10}>10</option>
                <option value={15}>15</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>

        {/* Auctions Table */}
        <DataTable
          data={auctions}
          columns={columns}
          loading={loading}
          pagination={pagination}
          onPageChange={(page) => setFilters({ ...filters, page })}
          actions={(auction) => (
            <div className="flex space-x-2">
              <button
                onClick={() => router.push(`/auctions/${auction.id}`)}
                className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
              >
                View
              </button>
              <button
                onClick={() => openEditModal(auction)}
                className="text-blue-600 hover:text-blue-900 text-sm font-medium"
              >
                Edit
              </button>
              <button
                onClick={() => openImageModal(auction)}
                className="text-purple-600 hover:text-purple-900 text-sm font-medium"
              >
                Images
              </button>
              <button
                onClick={() => handleDeleteAuction(auction.id)}
                className="text-red-600 hover:text-red-900 text-sm font-medium"
              >
                Delete
              </button>
            </div>
          )}
          emptyMessage="No auctions found. Create your first auction to get started."
        />

        {/* Create Auction Modal */}
        <Modal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          title="Create New Auction"
          size="xl"
        >
          <CreateAuctionFormComponent
            categories={categories}
            onSubmit={handleCreateAuction}
            onCancel={() => setShowCreateModal(false)}
            loading={loading}
          />
        </Modal>

        {/* Edit Auction Modal */}
        <Modal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          title="Edit Auction"
          size="lg"
        >
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              <p>
                Auction editing is limited to certain fields to maintain auction
                integrity.
              </p>
            </div>

            {selectedAuction && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Auction Title
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {selectedAuction.title}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Current Status
                  </label>
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      selectedAuction.status === "active"
                        ? "bg-green-100 text-green-800"
                        : selectedAuction.status === "draft"
                        ? "bg-gray-100 text-gray-800"
                        : selectedAuction.status === "completed"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {selectedAuction.status}
                  </span>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Current Price
                  </label>
                  <p className="mt-1 text-lg font-semibold text-green-600">
                    ${parseFloat(selectedAuction.current_price).toFixed(2)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    End Time
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(selectedAuction.end_time).toLocaleString()}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Auction Items ({selectedAuction.auction_items?.length || 0})
                  </label>
                  <div className="mt-2 space-y-2">
                    {selectedAuction.auction_items?.map((item, index) => (
                      <div key={item.id} className="p-3 bg-gray-50 rounded-lg">
                        <p className="font-medium text-gray-900">
                          {item.item_name}
                        </p>
                        {item.item_description && (
                          <p className="text-sm text-gray-600 mt-1">
                            {item.item_description}
                          </p>
                        )}
                        <p className="text-xs text-gray-500 mt-1">
                          {item.images?.length || 0} image(s)
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end space-x-3 pt-4 border-t">
              <button
                type="button"
                onClick={() => setShowEditModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Close
              </button>
              <button
                type="button"
                onClick={() => router.push(`/auctions/${selectedAuction?.id}`)}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
              >
                View Full Details
              </button>
            </div>
          </div>
        </Modal>

        {/* Auction Images Modal */}
        <Modal
          isOpen={showImageModal}
          onClose={() => setShowImageModal(false)}
          title={`Auction Images - ${selectedAuction?.title || ""}`}
          size="xl"
        >
          <div className="space-y-6">
            {imagesLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading images...</p>
              </div>
            ) : auctionImages && auctionImages.length > 0 ? (
              <div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {auctionImages.map((image) => (
                    <div key={image.id} className="relative group">
                      <div className="aspect-square rounded-lg overflow-hidden bg-white border">
                        <img
                          src={image.url}
                          alt={image.original_name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <button
                        type="button"
                        onClick={() => handleDeleteAuctionImage(image.id)}
                        className="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <svg
                          className="h-3 w-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-4">
                  Click the × button to remove individual images. Note: Removing
                  images from active auctions may affect bidder confidence.
                </p>
              </div>
            ) : (
              <div className="p-8 text-center">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <p className="mt-2 text-gray-500">
                  No images found for this auction.
                </p>
              </div>
            )}
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default VendorAuctionsPage;
