"use client";

import React, { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { AuctionModel } from "@/types/auction";
import AuctionService from "@/modules/auctions/services/AuctionService";
import useGlobalService from "@/core/hook/useGlobalService";
import VendorBidManagement from "@/components/vendor/VendorBidManagement";
import { LoadingSpinner } from "@/components/common/LoadingSpinner";
import { ErrorMessage } from "@/components/common/ErrorMessage";
import Link from "next/link";

const VendorAuctionBidsPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const [auction, setAuction] = useState<AuctionModel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const auctionService = useGlobalService(AuctionService);

  const auctionId = params?.id ? parseInt(params.id as string) : null;

  useEffect(() => {
    if (status === "loading") return;

    if (!session?.user) {
      router.push("/auth/signin");
      return;
    }

    if (!auctionId) {
      setError("Invalid auction ID");
      setLoading(false);
      return;
    }

    fetchAuction();
  }, [session, status, auctionId]);

  const fetchAuction = async () => {
    if (!auctionId) return;

    try {
      setLoading(true);
      setError(null);
      const response = await auctionService.methods.getAuction({
        id: auctionId,
      });

      // Check if user is a vendor and owns this auction
      if (!session || !(session.user as any)?.roles?.includes("vendor")) {
        setError("You must be a vendor to view auction bids.");
        return;
      }

      // For now, we'll allow any vendor to view any auction bids
      // In a real implementation, you'd check if the current user's vendor ID matches the auction's vendor ID
      // This would require fetching the current user's vendor profile

      setAuction(response);
    } catch (err: any) {
      setError(err.message || "Failed to load auction");
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Link
              href="/dashboard/vendor/auctions"
              className="inline-flex items-center text-sm font-medium text-gray-500 hover:text-gray-700"
            >
              <svg
                className="mr-2 h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Back to Auctions
            </Link>
          </div>
          <ErrorMessage message={error} onRetry={fetchAuction} />
        </div>
      </div>
    );
  }

  if (!auction) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "ended":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-purple-100 text-purple-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Breadcrumb */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-4">
              <li>
                <Link
                  href="/dashboard/vendor"
                  className="text-gray-400 hover:text-gray-500"
                >
                  Dashboard
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg
                    className="flex-shrink-0 h-5 w-5 text-gray-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <Link
                    href="/dashboard/vendor/auctions"
                    className="ml-4 text-gray-400 hover:text-gray-500"
                  >
                    Auctions
                  </Link>
                </div>
              </li>
              <li>
                <div className="flex items-center">
                  <svg
                    className="flex-shrink-0 h-5 w-5 text-gray-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="ml-4 text-gray-500">Bids</span>
                </div>
              </li>
            </ol>
          </nav>
        </div>

        {/* Auction Header */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {auction.title}
                </h1>
                <p className="text-gray-600 mt-1">{auction.description}</p>
              </div>
              <div className="flex items-center space-x-4">
                <span
                  className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(
                    auction.status
                  )}`}
                >
                  {auction.status.toUpperCase()}
                </span>
                {auction.auction_type === "sealed" && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                    SEALED
                  </span>
                )}
              </div>
            </div>
            <div className="mt-4 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm text-gray-500">Starting Price</div>
                <div className="text-lg font-semibold text-gray-900">
                  {formatCurrency(parseFloat(auction.starting_price))}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Current Price</div>
                <div className="text-lg font-semibold text-green-600">
                  {formatCurrency(parseFloat(auction.current_price))}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Start Time</div>
                <div className="text-lg font-semibold text-gray-900">
                  {new Date(auction.start_time).toLocaleDateString()}
                </div>
              </div>
              <div>
                <div className="text-sm text-gray-500">End Time</div>
                <div className="text-lg font-semibold text-gray-900">
                  {new Date(auction.end_time).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bid Management Component */}
        <VendorBidManagement auction={auction} />
      </div>
    </div>
  );
};

export default VendorAuctionBidsPage;
