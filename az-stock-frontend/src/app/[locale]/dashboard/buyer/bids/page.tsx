"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import DataTable from "@/modules/dashboard/components/shared/DataTable";
import { buyerService } from "@/modules/dashboard/services/BuyerService";
import {
  BidModel,
  BidFilters,
} from "@/modules/dashboard/models/DashboardModels";

const BuyerBidsPage: React.FC = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [bids, setBids] = useState<BidModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState<any>(null);
  const [filters, setFilters] = useState<BidFilters>({ per_page: 15, page: 1 });

  useEffect(() => {
    if (status === "loading") return;

    if (!session || !(session.user as any)?.roles?.includes("buyer")) {
      router.push("/");
      return;
    }

    loadBids();
  }, [session, status, router, filters]);

  const loadBids = async () => {
    try {
      setLoading(true);
      const response = await buyerService.getMyBids(filters);
      setBids(response.data);
      setPagination(response.meta);
    } catch (error) {
      console.error("Error loading bids:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelBid = async (bidId: number) => {
    if (!confirm("Are you sure you want to cancel this bid?")) return;

    try {
      await buyerService.cancelBid(bidId);
      loadBids(); // Reload the bids list
    } catch (error) {
      console.error("Error canceling bid:", error);
      alert("Failed to cancel bid. Please try again.");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusClasses = {
      active: "bg-green-100 text-green-800",
      won: "bg-blue-100 text-blue-800",
      lost: "bg-red-100 text-red-800",
      cancelled: "bg-gray-100 text-gray-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          statusClasses[status as keyof typeof statusClasses] ||
          "bg-gray-100 text-gray-800"
        }`}
      >
        {status}
      </span>
    );
  };

  const columns = [
    {
      key: "id",
      header: "Bid ID",
      sortable: true,
    },
    {
      key: "auction",
      header: "Auction",
      render: (bid: BidModel) => (
        <div>
          <div className="font-medium text-gray-900">
            {bid.auction?.product?.name || "Unknown Product"}
          </div>
          <div className="text-sm text-gray-500">
            Auction #{bid.auction?.id}
          </div>
        </div>
      ),
    },
    {
      key: "bid_amount",
      header: "Bid Amount",
      render: (bid: BidModel) => (
        <span className="font-medium text-gray-900">
          ${parseFloat(bid.bid_amount).toFixed(2)}
        </span>
      ),
    },
    {
      key: "status",
      header: "Status",
      render: (bid: BidModel) => getStatusBadge(bid.status),
    },
    {
      key: "created_at",
      header: "Bid Date",
      render: (bid: BidModel) => new Date(bid.created_at).toLocaleDateString(),
    },
    {
      key: "auction_status",
      header: "Auction Status",
      render: (bid: BidModel) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            bid.auction?.status === "active"
              ? "bg-green-100 text-green-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {bid.auction?.status || "Unknown"}
        </span>
      ),
    },
  ];

  if (status === "loading" || !session) {
    return <div>Loading...</div>;
  }

  if (!(session.user as any)?.roles?.includes("buyer")) {
    return <div>Access denied</div>;
  }

  return (
    <div className="space-y-6">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">My Bids</h1>
          <p className="mt-1 text-sm text-gray-500">
            Track all your auction bids and their status
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                value={filters.status || ""}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    status: e.target.value as any,
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="won">Won</option>
                <option value="lost">Lost</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">
                Per Page
              </label>
              <select
                value={filters.per_page || 15}
                onChange={(e) =>
                  setFilters({
                    ...filters,
                    per_page: parseInt(e.target.value),
                    page: 1,
                  })
                }
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
              >
                <option value={10}>10</option>
                <option value={15}>15</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>

        {/* Bids Table */}
        <DataTable
          data={bids}
          columns={columns}
          loading={loading}
          pagination={pagination}
          onPageChange={(page) => setFilters({ ...filters, page })}
          actions={(bid) => (
            <div className="flex space-x-2">
              {bid.auction && (
                <button
                  onClick={() => router.push(`/auctions/${bid.auction?.id}`)}
                  className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                >
                  View Auction
                </button>
              )}
              {bid.status === "active" && bid.auction?.status === "active" && (
                <button
                  onClick={() => handleCancelBid(bid.id)}
                  className="text-red-600 hover:text-red-900 text-sm font-medium"
                >
                  Cancel
                </button>
              )}
            </div>
          )}
          emptyMessage="You haven't placed any bids yet. Start bidding on auctions to see them here."
        />

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {bids.filter((bid) => bid.status === "active").length}
              </div>
              <div className="text-sm text-gray-500">Active Bids</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {bids.filter((bid) => bid.status === "won").length}
              </div>
              <div className="text-sm text-gray-500">Won Auctions</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {bids.filter((bid) => bid.status === "lost").length}
              </div>
              <div className="text-sm text-gray-500">Lost Bids</div>
            </div>
          </div>
          <div className="bg-white shadow rounded-lg p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {bids.filter((bid) => bid.status === "cancelled").length}
              </div>
              <div className="text-sm text-gray-500">Cancelled Bids</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BuyerBidsPage;
