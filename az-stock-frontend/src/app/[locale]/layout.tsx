import type { Metadata } from "next";
import "../globals.css";
import Footer from "@/modules/common/components/Footer/Footer";
import AosInit from "@/core/hook/AosInit";
import { notFound } from "next/navigation";
import localFont from "next/font/local";
import Providers from "./providers";
import { CartProvider } from "@/modules/common/contexts/Store";
import { hasLocale, NextIntlClientProvider } from "next-intl";
import { routing } from "@/i18n/routing";
import { getMessages } from "next-intl/server";

const PingARLT = localFont({
  src: [
    { path: "../../config/PingARLT-Regular.woff2", weight: "400" },
    { path: "../../config/PingARLT-Medium.woff2", weight: "500" },
  ],
  variable: "--font-pingARLT",
});

export const metadata: Metadata = {
  title: "Az stock",
};

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  const messages = await getMessages();
  try {
  } catch (error) {
    return notFound();
  }
  console.log(locale);
  return (
    <html lang={locale} style={{ direction: locale === "ar" ? "rtl" : "ltr" }}>
      <body className={`${PingARLT.className} `}>
        <AosInit />
        <Providers>
          <NextIntlClientProvider messages={messages}>
            <CartProvider>{children}</CartProvider>
          </NextIntlClientProvider>
        </Providers>
      </body>
    </html>
  );
}
