"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import "../globals.css";

import { SessionProvider as NextAuthSessionProvider } from "next-auth/react";
import { SessionProvider as CustomSessionProvider } from "@/components/SessionProvider";
import { useState } from "react";

const Providers: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <NextAuthSessionProvider>
        <CustomSessionProvider
          autoRefreshInterval={5 * 60 * 1000} // 5 minutes
          enableAutoRefresh={true}
        >
          {children}
        </CustomSessionProvider>
      </NextAuthSessionProvider>
    </QueryClientProvider>
  );
};

export default Providers;
