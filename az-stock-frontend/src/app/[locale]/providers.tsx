"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import "../globals.css";

import { SessionProvider, useSession } from "next-auth/react";
import { useState } from "react";
const Providers: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider >{children}</SessionProvider>
    </QueryClientProvider>
  );
};

export default Providers;
