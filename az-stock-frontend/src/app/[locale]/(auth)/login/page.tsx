import LoginForm from "@/modules/auth/components/LoginForm";
import { Metadata } from "next";

export async function generateMetadata(): Promise<Metadata> {
  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_URL!),
    title: "Login",

    openGraph: {
      url: `${process.env.NEXT_PUBLIC_URL!}/cart`,
    },
    alternates: {
      canonical: "/cart",
    },
  };
}

const Page = () => {
  return <LoginForm />;
};

export default Page;
