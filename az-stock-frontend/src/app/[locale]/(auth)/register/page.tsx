import RegisterForm from "@/modules/auth/components/RegisterForm";
import { Metadata } from "next";

export async function generateMetadata(): Promise<Metadata> {
  return {
    metadataBase: new URL(process.env.NEXT_PUBLIC_URL!),
    title: "Register - Sample Store",
    openGraph: {
      url: `${process.env.NEXT_PUBLIC_URL!}/register`,
    },
    alternates: {
      canonical: "/register",
    },
  };
}

const Page = () => {
  return <RegisterForm />;
};

export default Page;
