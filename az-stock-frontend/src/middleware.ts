import { withAuth } from "next-auth/middleware";
import { NextRequest, NextResponse } from "next/server";
import createMiddleware from "next-intl/middleware";
import { _localePrefix, _locales } from "./utils";
import { routing } from "./i18n/routing";

// Create next-intl middleware
const intlMiddleware = createMiddleware(routing);

// Wrap withAuth for authentication
const authMiddleware = withAuth(
  async (req) => {
    const pathname = req.nextUrl.pathname;

    // Redirect unauthenticated users accessing /dashboard
    if (pathname.startsWith("/dashboard") && !req.nextauth.token) {
      return NextResponse.redirect(new URL("/login", req.url));
    }

    return intlMiddleware(req); // Chain to intl middleware
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        if (pathname === "/login") return true;

        if (pathname.startsWith("/dashboard")) return !!token;

        return true;
      },
    },
  }
);

// Final middleware entry point
export function middleware(req: NextRequest) {
  const pathname = req.nextUrl.pathname;

  if (pathname.startsWith("/dashboard")) {
    return (authMiddleware as any)(req);
  }

  // Always return a Response from intlMiddleware for other routes
  return intlMiddleware(req);
}

export const config = {
  matcher: [
    "/dashboard/:path*",
    "/",
    "/((?!api|_next|_vercel|api/proxy|.*\\..*).*)",
    "/(ar|en)/:path*",
  ],
};
