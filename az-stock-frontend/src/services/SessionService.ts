"use client";

import { getSession, signOut } from "next-auth/react";

export interface SessionUpdateData {
  user?: {
    id?: string;
    name?: string;
    email?: string;
    role?: "admin" | "vendor" | "buyer";
    roles?: ("admin" | "vendor" | "buyer")[];
    verification_status?: {
      buyer?: boolean;
      vendor?: boolean;
      admin?: boolean;
    };
  };
  accessToken?: string;
}

export interface SessionRefreshOptions {
  force?: boolean;
  silent?: boolean;
  onSuccess?: (session: any) => void;
  onError?: (error: string) => void;
}

class SessionService {
  private static instance: SessionService;
  private refreshPromise: Promise<any> | null = null;
  private lastRefreshTime: number = 0;
  private readonly REFRESH_COOLDOWN = 2000; // 2 seconds cooldown between refreshes

  private constructor() {}

  public static getInstance(): SessionService {
    if (!SessionService.instance) {
      SessionService.instance = new SessionService();
    }
    return SessionService.instance;
  }

  /**
   * Refresh the session by fetching updated user data from the backend
   */
  public async refreshSession(options: SessionRefreshOptions = {}): Promise<any> {
    const { force = false, silent = false, onSuccess, onError } = options;

    // Check cooldown period
    const now = Date.now();
    if (!force && now - this.lastRefreshTime < this.REFRESH_COOLDOWN) {
      if (!silent) {
        console.log("Session refresh skipped due to cooldown");
      }
      return null;
    }

    // Return existing promise if refresh is already in progress
    if (this.refreshPromise && !force) {
      if (!silent) {
        console.log("Session refresh already in progress, waiting...");
      }
      return this.refreshPromise;
    }

    this.refreshPromise = this.performRefresh(silent);
    this.lastRefreshTime = now;

    try {
      const result = await this.refreshPromise;
      if (onSuccess) {
        onSuccess(result);
      }
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      if (onError) {
        onError(errorMessage);
      }
      throw error;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual session refresh
   */
  private async performRefresh(silent: boolean = false): Promise<any> {
    try {
      const session = await getSession();

      if (!session?.accessToken) {
        throw new Error("No access token available");
      }

      if (!silent) {
        console.log("Fetching updated user data...");
      }

      // Fetch updated user data from the backend
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/user`, {
        headers: {
          'Authorization': `Bearer ${session.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token is invalid, sign out the user
          if (!silent) {
            console.log("Token expired, signing out user");
          }
          await signOut({ redirect: false });
          throw new Error("Session expired");
        }
        throw new Error(`Failed to fetch user data: ${response.statusText}`);
      }

      const userData = await response.json();

      if (!userData.data) {
        throw new Error("Invalid user data response");
      }

      // Trigger session update using NextAuth's update mechanism
      const { update } = await import("next-auth/react");
      const updatedSession = await update({
        user: {
          id: userData.data.id,
          name: userData.data.name,
          email: userData.data.email,
          role: userData.data.role,
          roles: userData.data.roles,
          verification_status: userData.data.verification_status,
        },
        accessToken: session.accessToken,
      });

      if (!silent) {
        console.log("Session updated successfully");
      }

      return updatedSession;

    } catch (error) {
      if (!silent) {
        console.error("Session refresh failed:", error);
      }
      throw error;
    }
  }

  /**
   * Update session with specific data (useful for immediate updates after API calls)
   */
  public async updateSessionData(data: SessionUpdateData): Promise<any> {
    try {
      const session = await getSession();
      
      if (!session) {
        throw new Error("No active session");
      }

      const { update } = await import("next-auth/react");
      
      const updatedSession = await update({
        user: {
          ...session.user,
          ...data.user,
        },
        accessToken: data.accessToken || session.accessToken,
      });

      console.log("Session data updated:", updatedSession);
      return updatedSession;

    } catch (error) {
      console.error("Failed to update session data:", error);
      throw error;
    }
  }

  /**
   * Refresh session silently (without console logs)
   */
  public async refreshSessionSilently(): Promise<any> {
    return this.refreshSession({ silent: true });
  }

  /**
   * Force refresh session (bypasses cooldown and concurrent request checks)
   */
  public async forceRefreshSession(): Promise<any> {
    return this.refreshSession({ force: true });
  }

  /**
   * Check if the current user has a specific role
   */
  public async hasRole(role: "admin" | "vendor" | "buyer"): Promise<boolean> {
    try {
      const session = await getSession();
      const roles = session?.user?.roles || [];
      return roles.includes(role);
    } catch (error) {
      console.error("Error checking user role:", error);
      return false;
    }
  }

  /**
   * Check if the current user is verified for a specific role
   */
  public async isVerifiedFor(role: "admin" | "vendor" | "buyer"): Promise<boolean> {
    try {
      const session = await getSession();
      const verificationStatus = session?.user?.verification_status || {};
      return verificationStatus[role] === true;
    } catch (error) {
      console.error("Error checking verification status:", error);
      return false;
    }
  }

  /**
   * Get current session data
   */
  public async getCurrentSession(): Promise<any> {
    return getSession();
  }

  /**
   * Clear refresh state (useful for testing or manual reset)
   */
  public clearRefreshState(): void {
    this.refreshPromise = null;
    this.lastRefreshTime = 0;
  }
}

// Export singleton instance
export const sessionService = SessionService.getInstance();
export default sessionService;
