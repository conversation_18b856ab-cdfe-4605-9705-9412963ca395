"use client";

export interface OtpRequestResponse {
  success: boolean;
  message: string;
  expires_at?: string;
  expires_in_seconds?: number;
  error_code?: string;
  wait_time?: number;
}

export interface OtpVerificationResponse {
  success: boolean;
  message: string;
  data?: any;
  token?: string;
  error_code?: string;
  attempts_remaining?: number;
}

class OtpService {
  private static instance: OtpService;
  private baseUrl: string;

  private constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || "";
  }

  public static getInstance(): OtpService {
    if (!OtpService.instance) {
      OtpService.instance = new OtpService();
    }
    return OtpService.instance;
  }

  /**
   * Request OTP code
   */
  public async requestOtp(
    email: string,
    purpose: string = "login"
  ): Promise<OtpRequestResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/otp/request`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
        body: JSON.stringify({
          email,
          purpose,
        }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("OTP request error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
        error_code: "NETWORK_ERROR",
      };
    }
  }

  /**
   * Verify OTP code
   */
  public async verifyOtp(
    email: string,
    code: string,
    purpose: string = "login"
  ): Promise<OtpVerificationResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/otp/verify`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
        body: JSON.stringify({
          email,
          code,
          purpose,
        }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("OTP verification error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
        error_code: "NETWORK_ERROR",
      };
    }
  }

  /**
   * Resend OTP code
   */
  public async resendOtp(
    email: string,
    purpose: string = "login"
  ): Promise<OtpRequestResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/otp/resend`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
        body: JSON.stringify({
          email,
          purpose,
        }),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("OTP resend error:", error);
      return {
        success: false,
        message: "Network error. Please check your connection and try again.",
        error_code: "NETWORK_ERROR",
      };
    }
  }

  /**
   * Validate email format
   */
  public validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate OTP code format
   */
  public validateOtpCode(code: string): boolean {
    const otpRegex = /^\d{6}$/;
    return otpRegex.test(code);
  }

  /**
   * Format time remaining
   */
  public formatTimeRemaining(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  }

  /**
   * Calculate time remaining until expiry
   */
  public getTimeRemaining(expiresAt: string): number {
    const now = new Date().getTime();
    const expiry = new Date(expiresAt).getTime();
    return Math.max(0, Math.floor((expiry - now) / 1000));
  }

  /**
   * Check if OTP has expired
   */
  public isExpired(expiresAt: string): boolean {
    return this.getTimeRemaining(expiresAt) <= 0;
  }

  /**
   * Get user-friendly error message
   */
  public getErrorMessage(errorCode: string, defaultMessage: string): string {
    const errorMessages: Record<string, string> = {
      RATE_LIMITED: "Too many requests. Please wait before trying again.",
      IP_RATE_LIMITED: "Too many requests from your location. Please try again later.",
      VERIFY_RATE_LIMITED: "Too many verification attempts. Please try again later.",
      USER_NOT_FOUND: "No account found with this email address.",
      OTP_NOT_FOUND: "Invalid or expired OTP code.",
      INVALID_CODE: "The OTP code you entered is incorrect.",
      TOO_MANY_ATTEMPTS: "Too many verification attempts. Please request a new OTP.",
      SEND_FAILED: "Failed to send OTP. Please try again.",
      VERIFICATION_ERROR: "Verification failed. Please try again.",
      NETWORK_ERROR: "Network error. Please check your connection.",
    };

    return errorMessages[errorCode] || defaultMessage;
  }

  /**
   * Get retry delay based on error code
   */
  public getRetryDelay(errorCode: string, waitTime?: number): number {
    if (waitTime) {
      return waitTime;
    }

    const defaultDelays: Record<string, number> = {
      RATE_LIMITED: 60,
      IP_RATE_LIMITED: 300,
      VERIFY_RATE_LIMITED: 300,
      TOO_MANY_ATTEMPTS: 600,
    };

    return defaultDelays[errorCode] || 0;
  }
}

// Export singleton instance
export const otpService = OtpService.getInstance();
export default otpService;
