import { put, del } from '@vercel/blob';

export interface UploadResult {
  url: string;
  pathname: string;
  contentType: string;
  contentDisposition: string;
  size: number;
}

export interface UploadError {
  file: File;
  error: string;
}

export interface BulkUploadResult {
  successful: UploadResult[];
  failed: UploadError[];
}

class VercelBlobService {
  private token: string;
  constructor() {

    this.token = process.env.BLOB_READ_WRITE_TOKEN || '';
    if (!this.token) {
      console.warn('BLOB_READ_WRITE_TOKEN is not set. Vercel Blob uploads will fail.');
    }
  }

  /**
   * Upload a single file to Vercel Blob Storage
   */
  async uploadFile(file: File, filename?: string): Promise<UploadResult> {
    console.log(this.token)
    if (!this.token) {
      throw new Error('Vercel Blob token is not configured');
    }

    // Validate file before upload
    this.validateFile(file);

    try {
      const finalFilename = filename || this.generateFilename(file);

      const blob = await put(finalFilename, file, {
        access: 'public',
        token: this.token,
      });

      return {
        url: blob.url,
        pathname: blob.pathname,
        contentType: blob.contentType || file.type,
        contentDisposition: blob.contentDisposition || '',
        size: blob.size || file.size,
      };
    } catch (error) {
      console.error('Vercel Blob upload failed:', error);
      throw new Error(`Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload multiple files to Vercel Blob Storage
   */
  async uploadFiles(files: File[], directory?: string): Promise<BulkUploadResult> {
    const successful: UploadResult[] = [];
    const failed: UploadError[] = [];

    for (const file of files) {
      try {
        const filename = directory 
          ? `${directory}/${this.generateFilename(file)}`
          : this.generateFilename(file);
        
        const result = await this.uploadFile(file, filename);
        successful.push(result);
      } catch (error) {
        failed.push({
          file,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return { successful, failed };
  }

  /**
   * Delete a file from Vercel Blob Storage
   */
  async deleteFile(url: string): Promise<boolean> {
    if (!this.token) {
      throw new Error('Vercel Blob token is not configured');
    }

    try {
      await del(url, { token: this.token });
      return true;
    } catch (error) {
      console.error('Vercel Blob delete failed:', error);
      return false;
    }
  }

  /**
   * Delete multiple files from Vercel Blob Storage
   */
  async deleteFiles(urls: string[]): Promise<{ deleted: string[]; failed: string[] }> {
    const deleted: string[] = [];
    const failed: string[] = [];

    for (const url of urls) {
      try {
        const success = await this.deleteFile(url);
        if (success) {
          deleted.push(url);
        } else {
          failed.push(url);
        }
      } catch (error) {
        failed.push(url);
      }
    }

    return { deleted, failed };
  }

  /**
   * Generate a unique filename for upload
   */
  private generateFilename(file: File): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const randomString = Math.random().toString(36).substring(2, 8);
    const extension = file.name.split('.').pop() || '';
    
    return `${timestamp}_${randomString}.${extension}`;
  }

  /**
   * Generate a filename with directory structure
   */
  generateDirectoryFilename(file: File, directory: string): string {
    const filename = this.generateFilename(file);
    return `${directory}/${filename}`;
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    return !!this.token;
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File): void {
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error(`File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds maximum allowed size of 10MB`);
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      throw new Error(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
    }
  }

  /**
   * Get the base URL for Vercel Blob Storage
   */
  getBaseUrl(): string {
    return 'https://cjtssc4w6vlftqkj.public.blob.vercel-storage.com';
  }
}

// Export a singleton instance
export const vercelBlobService = new VercelBlobService();
export default VercelBlobService;
