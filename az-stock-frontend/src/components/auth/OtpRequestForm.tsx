"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const otpRequestSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

type OtpRequestFormData = z.infer<typeof otpRequestSchema>;

interface OtpRequestFormProps {
  onSuccess: (email: string, expiresAt: string) => void;
  onError: (error: string) => void;
  isLoading?: boolean;
}

const OtpRequestForm: React.FC<OtpRequestFormProps> = ({
  onSuccess,
  onError,
  isLoading = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues,
  } = useForm<OtpRequestFormData>({
    resolver: zodResolver(otpRequestSchema),
  });

  const onSubmit = async (data: OtpRequestFormData) => {
    if (isSubmitting || isLoading) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/otp/request`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          purpose: "login",
        }),
      });

      const result = await response.json();

      if (result.success) {
        onSuccess(data.email, result.expires_at);
      } else {
        onError(result.message || "Failed to send OTP. Please try again.");
      }
    } catch (error) {
      console.error("OTP request error:", error);
      onError("Network error. Please check your connection and try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResend = async () => {
    const email = getValues("email");
    if (!email) {
      onError("Please enter your email address first.");
      return;
    }

    if (isSubmitting || isLoading) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/otp/resend`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
        body: JSON.stringify({
          email: email,
          purpose: "login",
        }),
      });

      const result = await response.json();

      if (result.success) {
        onSuccess(email, result.expires_at);
      } else {
        onError(result.message || "Failed to resend OTP. Please try again.");
      }
    } catch (error) {
      console.error("OTP resend error:", error);
      onError("Network error. Please check your connection and try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Sign In with OTP</h2>
          <p className="text-gray-600 mt-2">
            Enter your email address and we'll send you a verification code
          </p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email Address
            </label>
            <input
              {...register("email")}
              type="email"
              id="email"
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.email ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Enter your email address"
              disabled={isSubmitting || isLoading}
            />
            {errors.email && (
              <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
            )}
          </div>

          <div className="space-y-3">
            <button
              type="submit"
              disabled={isSubmitting || isLoading}
              className={`w-full py-2 px-4 rounded-md text-white font-medium transition-colors ${
                isSubmitting || isLoading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }`}
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Sending OTP...
                </div>
              ) : (
                "Send Verification Code"
              )}
            </button>

            <button
              type="button"
              onClick={handleResend}
              disabled={isSubmitting || isLoading}
              className={`w-full py-2 px-4 rounded-md border font-medium transition-colors ${
                isSubmitting || isLoading
                  ? "border-gray-300 text-gray-400 cursor-not-allowed"
                  : "border-gray-300 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              }`}
            >
              Resend Code
            </button>
          </div>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            Don't have an account?{" "}
            <a href="/register" className="text-blue-600 hover:text-blue-500 font-medium">
              Sign up here
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default OtpRequestForm;
