"use client";

import React, { useState, useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

const otpVerificationSchema = z.object({
  code: z.string().length(6, "OTP code must be 6 digits").regex(/^\d+$/, "OTP code must contain only numbers"),
});

type OtpVerificationFormData = z.infer<typeof otpVerificationSchema>;

interface OtpVerificationFormProps {
  email: string;
  expiresAt: string;
  onSuccess: (userData: any, token: string) => void;
  onError: (error: string) => void;
  onBack: () => void;
  onResend: () => void;
  isLoading?: boolean;
}

const OtpVerificationForm: React.FC<OtpVerificationFormProps> = ({
  email,
  expiresAt,
  onSuccess,
  onError,
  onBack,
  onResend,
  isLoading = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [otpInputs, setOtpInputs] = useState(["", "", "", "", "", ""]);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const {
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<OtpVerificationFormData>({
    resolver: zodResolver(otpVerificationSchema),
  });

  const watchedCode = watch("code");

  // Calculate time remaining
  useEffect(() => {
    const calculateTimeRemaining = () => {
      const now = new Date().getTime();
      const expiry = new Date(expiresAt).getTime();
      const remaining = Math.max(0, Math.floor((expiry - now) / 1000));
      setTimeRemaining(remaining);
    };

    calculateTimeRemaining();
    const interval = setInterval(calculateTimeRemaining, 1000);

    return () => clearInterval(interval);
  }, [expiresAt]);

  // Format time remaining
  const formatTimeRemaining = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // Handle OTP input change
  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) {
      // Handle paste
      const pastedCode = value.slice(0, 6);
      const newInputs = [...otpInputs];
      for (let i = 0; i < 6; i++) {
        newInputs[i] = pastedCode[i] || "";
      }
      setOtpInputs(newInputs);
      setValue("code", newInputs.join(""));
      
      // Focus on the last filled input or the next empty one
      const nextIndex = Math.min(pastedCode.length, 5);
      inputRefs.current[nextIndex]?.focus();
    } else {
      // Handle single character input
      const newInputs = [...otpInputs];
      newInputs[index] = value;
      setOtpInputs(newInputs);
      setValue("code", newInputs.join(""));

      // Move to next input
      if (value && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  // Handle backspace
  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && !otpInputs[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const onSubmit = async (data: OtpVerificationFormData) => {
    if (isSubmitting || isLoading) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/otp/verify`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
        },
        body: JSON.stringify({
          email: email,
          code: data.code,
          purpose: "login",
        }),
      });

      const result = await response.json();

      if (result.success) {
        onSuccess(result.data, result.token);
      } else {
        onError(result.message || "Invalid OTP code. Please try again.");
        // Clear the OTP inputs on error
        setOtpInputs(["", "", "", "", "", ""]);
        setValue("code", "");
        inputRefs.current[0]?.focus();
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      onError("Network error. Please check your connection and try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Auto-submit when all 6 digits are entered
  useEffect(() => {
    if (watchedCode && watchedCode.length === 6) {
      handleSubmit(onSubmit)();
    }
  }, [watchedCode]);

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white shadow-lg rounded-lg p-6">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">Enter Verification Code</h2>
          <p className="text-gray-600 mt-2">
            We've sent a 6-digit code to
          </p>
          <p className="text-blue-600 font-medium">{email}</p>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3 text-center">
              Enter the 6-digit code
            </label>
            <div className="flex justify-center space-x-2">
              {otpInputs.map((digit, index) => (
                <input
                  key={index}
                  ref={(el) => (inputRefs.current[index] = el)}
                  type="text"
                  inputMode="numeric"
                  maxLength={6}
                  value={digit}
                  onChange={(e) => handleOtpChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  className={`w-12 h-12 text-center text-lg font-bold border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    errors.code ? "border-red-300" : "border-gray-300"
                  }`}
                  disabled={isSubmitting || isLoading}
                />
              ))}
            </div>
            {errors.code && (
              <p className="mt-2 text-sm text-red-600 text-center">{errors.code.message}</p>
            )}
          </div>

          {/* Timer */}
          <div className="text-center">
            {timeRemaining > 0 ? (
              <p className="text-sm text-gray-600">
                Code expires in{" "}
                <span className="font-medium text-blue-600">
                  {formatTimeRemaining(timeRemaining)}
                </span>
              </p>
            ) : (
              <p className="text-sm text-red-600 font-medium">
                Code has expired. Please request a new one.
              </p>
            )}
          </div>

          {/* Submit button (hidden, auto-submits) */}
          <button type="submit" className="hidden">
            Verify
          </button>
        </form>

        {/* Action buttons */}
        <div className="mt-6 space-y-3">
          <button
            type="button"
            onClick={onResend}
            disabled={isSubmitting || isLoading || timeRemaining > 0}
            className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
              isSubmitting || isLoading || timeRemaining > 0
                ? "bg-gray-100 text-gray-400 cursor-not-allowed"
                : "bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            }`}
          >
            {timeRemaining > 0 ? "Resend code" : "Resend code"}
          </button>

          <button
            type="button"
            onClick={onBack}
            disabled={isSubmitting || isLoading}
            className={`w-full py-2 px-4 rounded-md border font-medium transition-colors ${
              isSubmitting || isLoading
                ? "border-gray-300 text-gray-400 cursor-not-allowed"
                : "border-gray-300 text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            }`}
          >
            Back to Email
          </button>
        </div>

        {/* Loading indicator */}
        {isSubmitting && (
          <div className="mt-4 flex items-center justify-center">
            <svg className="animate-spin h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="ml-2 text-sm text-gray-600">Verifying...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default OtpVerificationForm;
