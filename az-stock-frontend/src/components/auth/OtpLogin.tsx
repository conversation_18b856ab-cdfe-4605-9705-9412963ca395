"use client";

import React, { useState } from "react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";
import OtpRequestForm from "./OtpRequestForm";
import OtpVerificationForm from "./OtpVerificationForm";

type OtpLoginStep = "request" | "verify";

interface OtpLoginProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

const OtpLogin: React.FC<OtpLoginProps> = ({
  onSuccess,
  redirectTo = "/dashboard",
}) => {
  const [currentStep, setCurrentStep] = useState<OtpLoginStep>("request");
  const [email, setEmail] = useState("");
  const [expiresAt, setExpiresAt] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  
  const router = useRouter();

  const handleOtpRequestSuccess = (userEmail: string, otpExpiresAt: string) => {
    setEmail(userEmail);
    setExpiresAt(otpExpiresAt);
    setCurrentStep("verify");
    setError("");
  };

  const handleOtpRequestError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleOtpVerificationSuccess = async (userData: any, token: string) => {
    setIsLoading(true);
    setError("");

    try {
      // Use NextAuth signIn with credentials
      const result = await signIn("credentials", {
        email: userData.email,
        token: token,
        redirect: false,
      });

      if (result?.error) {
        setError("Authentication failed. Please try again.");
        setCurrentStep("request");
        setIsLoading(false);
        return;
      }

      // Success - redirect or call onSuccess
      if (onSuccess) {
        onSuccess();
      } else {
        router.push(redirectTo);
      }
    } catch (error) {
      console.error("Authentication error:", error);
      setError("Authentication failed. Please try again.");
      setCurrentStep("request");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOtpVerificationError = (errorMessage: string) => {
    setError(errorMessage);
  };

  const handleBackToRequest = () => {
    setCurrentStep("request");
    setEmail("");
    setExpiresAt("");
    setError("");
  };

  const handleResendOtp = () => {
    setCurrentStep("request");
    setError("");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md">
        {/* Error Message */}
        {error && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Authentication Error
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Step Indicator */}
        <div className="mb-6">
          <div className="flex items-center justify-center">
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                currentStep === "request" 
                  ? "border-blue-600 bg-blue-600 text-white" 
                  : "border-green-600 bg-green-600 text-white"
              }`}>
                {currentStep === "request" ? "1" : "✓"}
              </div>
              <div className="ml-2 text-sm font-medium text-gray-900">
                Enter Email
              </div>
            </div>
            
            <div className={`mx-4 h-0.5 w-16 ${
              currentStep === "verify" ? "bg-green-600" : "bg-gray-300"
            }`}></div>
            
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                currentStep === "verify" 
                  ? "border-blue-600 bg-blue-600 text-white" 
                  : "border-gray-300 bg-gray-100 text-gray-400"
              }`}>
                2
              </div>
              <div className="ml-2 text-sm font-medium text-gray-900">
                Verify Code
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        {currentStep === "request" ? (
          <OtpRequestForm
            onSuccess={handleOtpRequestSuccess}
            onError={handleOtpRequestError}
            isLoading={isLoading}
          />
        ) : (
          <OtpVerificationForm
            email={email}
            expiresAt={expiresAt}
            onSuccess={handleOtpVerificationSuccess}
            onError={handleOtpVerificationError}
            onBack={handleBackToRequest}
            onResend={handleResendOtp}
            isLoading={isLoading}
          />
        )}

        {/* Alternative Login Options */}
        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-50 text-gray-500">Or</span>
            </div>
          </div>

          <div className="mt-6 text-center">
            <a
              href="/login"
              className="text-blue-600 hover:text-blue-500 font-medium text-sm"
            >
              Sign in with password instead
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OtpLogin;
