"use client";

import React, { createContext, useContext, useEffect, useRef, useState } from "react";
import { useSession } from "next-auth/react";
import { sessionService } from "@/services/SessionService";

interface SessionContextType {
  refreshSession: () => Promise<void>;
  isRefreshing: boolean;
  lastRefreshTime: number | null;
  refreshError: string | null;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

interface SessionProviderProps {
  children: React.ReactNode;
  autoRefreshInterval?: number; // in milliseconds, default 5 minutes
  enableAutoRefresh?: boolean;
}

export const SessionProvider: React.FC<SessionProviderProps> = ({
  children,
  autoRefreshInterval = 5 * 60 * 1000, // 5 minutes
  enableAutoRefresh = true,
}) => {
  const { data: session, status } = useSession();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefreshTime, setLastRefreshTime] = useState<number | null>(null);
  const [refreshError, setRefreshError] = useState<string | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef(true);

  // Manual refresh function
  const refreshSession = async () => {
    if (isRefreshing || !session) return;

    try {
      setIsRefreshing(true);
      setRefreshError(null);

      await sessionService.refreshSession({
        silent: false,
        onSuccess: () => {
          if (mountedRef.current) {
            setLastRefreshTime(Date.now());
          }
        },
        onError: (error) => {
          if (mountedRef.current) {
            setRefreshError(error);
          }
        },
      });
    } catch (error) {
      console.error("Manual session refresh failed:", error);
    } finally {
      if (mountedRef.current) {
        setIsRefreshing(false);
      }
    }
  };

  // Auto-refresh effect
  useEffect(() => {
    if (!enableAutoRefresh || status !== "authenticated" || !session) {
      return;
    }

    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Set up auto-refresh interval
    intervalRef.current = setInterval(async () => {
      if (!mountedRef.current || isRefreshing) return;

      try {
        await sessionService.refreshSessionSilently();
        if (mountedRef.current) {
          setLastRefreshTime(Date.now());
          setRefreshError(null);
        }
      } catch (error) {
        console.warn("Auto session refresh failed:", error);
        if (mountedRef.current) {
          setRefreshError(error instanceof Error ? error.message : "Auto refresh failed");
        }
      }
    }, autoRefreshInterval);

    // Cleanup function
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [session, status, enableAutoRefresh, autoRefreshInterval, isRefreshing]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Listen for visibility changes to refresh when tab becomes active
  useEffect(() => {
    if (!enableAutoRefresh || status !== "authenticated") return;

    const handleVisibilityChange = async () => {
      if (document.visibilityState === "visible" && !isRefreshing) {
        // Check if it's been more than 2 minutes since last refresh
        const timeSinceLastRefresh = lastRefreshTime ? Date.now() - lastRefreshTime : Infinity;
        if (timeSinceLastRefresh > 2 * 60 * 1000) { // 2 minutes
          try {
            await sessionService.refreshSessionSilently();
            if (mountedRef.current) {
              setLastRefreshTime(Date.now());
              setRefreshError(null);
            }
          } catch (error) {
            console.warn("Visibility change session refresh failed:", error);
          }
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [enableAutoRefresh, status, isRefreshing, lastRefreshTime]);

  const contextValue: SessionContextType = {
    refreshSession,
    isRefreshing,
    lastRefreshTime,
    refreshError,
  };

  return (
    <SessionContext.Provider value={contextValue}>
      {children}
    </SessionContext.Provider>
  );
};

// Hook to use the session context
export const useSessionContext = (): SessionContextType => {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error("useSessionContext must be used within a SessionProvider");
  }
  return context;
};

export default SessionProvider;
