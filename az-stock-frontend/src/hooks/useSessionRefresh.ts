"use client";

import { useSession } from "next-auth/react";
import { useCallback, useRef } from "react";

interface RefreshSessionOptions {
  force?: boolean;
  silent?: boolean;
}

interface SessionRefreshResult {
  success: boolean;
  error?: string;
}

export const useSessionRefresh = () => {
  const { data: session, update } = useSession();
  const refreshingRef = useRef(false);

  /**
   * Refresh the current session by fetching updated user data from the backend
   */
  const refreshSession = useCallback(async (
    options: RefreshSessionOptions = {}
  ): Promise<SessionRefreshResult> => {
    const { force = false, silent = false } = options;

    // Prevent multiple simultaneous refresh attempts
    if (refreshingRef.current && !force) {
      return { success: false, error: "Refresh already in progress" };
    }

    if (!session?.accessToken) {
      return { success: false, error: "No access token available" };
    }

    try {
      refreshingRef.current = true;

      if (!silent) {
        console.log("Refreshing session data...");
      }

      // Fetch updated user data from the backend
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/user`, {
        headers: {
          'Authorization': `Bearer ${session.accessToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user data: ${response.statusText}`);
      }

      const userData = await response.json();

      if (!userData.data) {
        throw new Error("Invalid user data response");
      }

      // Update the session with fresh data
      const updatedSession = await update({
        user: {
          id: userData.data.id,
          name: userData.data.name,
          email: userData.data.email,
          role: userData.data.role,
          roles: userData.data.roles,
          verification_status: userData.data.verification_status,
        },
        accessToken: session.accessToken, // Keep the same token
      });

      if (!silent) {
        console.log("Session refreshed successfully", updatedSession);
      }

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      
      if (!silent) {
        console.error("Failed to refresh session:", errorMessage);
      }

      return { success: false, error: errorMessage };
    } finally {
      refreshingRef.current = false;
    }
  }, [session, update]);

  /**
   * Refresh session silently (without console logs)
   */
  const refreshSessionSilently = useCallback(() => {
    return refreshSession({ silent: true });
  }, [refreshSession]);

  /**
   * Force refresh session (even if one is already in progress)
   */
  const forceRefreshSession = useCallback(() => {
    return refreshSession({ force: true });
  }, [refreshSession]);

  /**
   * Check if a session refresh is currently in progress
   */
  const isRefreshing = useCallback(() => {
    return refreshingRef.current;
  }, []);

  return {
    refreshSession,
    refreshSessionSilently,
    forceRefreshSession,
    isRefreshing,
  };
};

export default useSessionRefresh;
