<?php

namespace App\Console\Commands;

use App\Models\ProductImage;
use App\Models\Product;
use App\Services\VercelBlobService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;

class MigrateImagesToVercelBlob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vercel:migrate-images {--dry-run : Show what would be migrated without actually doing it}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing local images to Vercel Blob Storage';

    /**
     * Execute the console command.
     */
    public function handle(VercelBlobService $vercelBlobService)
    {
        if (!$vercelBlobService->isConfigured()) {
            $this->error('Vercel Blob Storage is not configured. Please set up your environment variables.');
            return 1;
        }

        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->info('DRY RUN MODE - No actual changes will be made');
        }

        $this->info('Starting image migration to Vercel Blob Storage...');

        // Migrate product images
        $this->migrateProductImages($vercelBlobService, $dryRun);
        
        // Migrate main product images
        $this->migrateMainProductImages($vercelBlobService, $dryRun);

        $this->info('Migration completed!');
        
        return 0;
    }

    private function migrateProductImages(VercelBlobService $vercelBlobService, bool $dryRun)
    {
        $this->info('Migrating product images...');
        
        $images = ProductImage::whereNotNull('image_path')
            ->where('image_path', 'not like', 'https://%')
            ->get();

        $this->info("Found {$images->count()} product images to migrate");

        foreach ($images as $image) {
            $this->line("Processing: {$image->image_path}");
            
            if ($dryRun) {
                $this->line("  → Would migrate to Vercel Blob");
                continue;
            }

            try {
                if (Storage::disk('public')->exists($image->image_path)) {
                    // Create a temporary UploadedFile from the existing file
                    $filePath = Storage::disk('public')->path($image->image_path);
                    $mimeType = Storage::disk('public')->mimeType($image->image_path);
                    $originalName = basename($image->image_path);
                    
                    $uploadedFile = new UploadedFile(
                        $filePath,
                        $originalName,
                        $mimeType,
                        null,
                        true
                    );

                    $filename = $vercelBlobService->generateFilename($uploadedFile, 'products/' . $image->product_id);
                    $result = $vercelBlobService->uploadFile($uploadedFile, $filename);

                    if ($result['success']) {
                        $image->update(['image_path' => $result['url']]);
                        $this->line("  ✓ Migrated successfully");
                    } else {
                        $this->error("  ✗ Failed to upload");
                    }
                } else {
                    $this->warn("  ! File not found locally");
                }
            } catch (\Exception $e) {
                $this->error("  ✗ Error: " . $e->getMessage());
            }
        }
    }

    private function migrateMainProductImages(VercelBlobService $vercelBlobService, bool $dryRun)
    {
        $this->info('Migrating main product images...');
        
        $products = Product::whereNotNull('image')
            ->where('image', 'not like', 'https://%')
            ->get();

        $this->info("Found {$products->count()} main product images to migrate");

        foreach ($products as $product) {
            $this->line("Processing: {$product->image}");
            
            if ($dryRun) {
                $this->line("  → Would migrate to Vercel Blob");
                continue;
            }

            try {
                if (Storage::disk('public')->exists($product->image)) {
                    $filePath = Storage::disk('public')->path($product->image);
                    $mimeType = Storage::disk('public')->mimeType($product->image);
                    $originalName = basename($product->image);
                    
                    $uploadedFile = new UploadedFile(
                        $filePath,
                        $originalName,
                        $mimeType,
                        null,
                        true
                    );

                    $filename = $vercelBlobService->generateFilename($uploadedFile, 'products/' . $product->id);
                    $result = $vercelBlobService->uploadFile($uploadedFile, $filename);

                    if ($result['success']) {
                        $product->update(['image' => $result['url']]);
                        $this->line("  ✓ Migrated successfully");
                    } else {
                        $this->error("  ✗ Failed to upload");
                    }
                } else {
                    $this->warn("  ! File not found locally");
                }
            } catch (\Exception $e) {
                $this->error("  ✗ Error: " . $e->getMessage());
            }
        }
    }
}
