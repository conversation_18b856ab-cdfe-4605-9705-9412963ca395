<?php

namespace App\Console\Commands;

use App\Services\VercelBlobService;
use Illuminate\Console\Command;

class TestVercelBlob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vercel:test-blob';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Vercel Blob Storage configuration';

    /**
     * Execute the console command.
     */
    public function handle(VercelBlobService $vercelBlobService)
    {
        $this->info('Testing Vercel Blob Storage Configuration...');
        
        // Check if service is configured
        if (!$vercelBlobService->isConfigured()) {
            $this->error('Vercel Blob Storage is not properly configured!');
            $this->error('Please check your environment variables:');
            $this->error('- VERCEL_BLOB_BASE_URL');
            $this->error('- VERCEL_BLOB_TOKEN');
            return 1;
        }
        
        $this->info('✓ Vercel Blob Storage is properly configured');
        
        // Display configuration
        $this->info('Configuration:');
        $this->line('Base URL: ' . config('services.vercel_blob.base_url'));
        $this->line('Store ID: ' . config('services.vercel_blob.store_id'));
        $this->line('Token: ' . (config('services.vercel_blob.token') ? 'Set' : 'Not Set'));
        
        $this->info('✓ Test completed successfully');
        
        return 0;
    }
}
