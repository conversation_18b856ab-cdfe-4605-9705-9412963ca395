<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class OtpCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'code_hash',
        'purpose',
        'expires_at',
        'is_used',
        'ip_address',
        'user_agent',
        'attempts',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_used' => 'boolean',
    ];

    /**
     * Generate a new OTP code
     */
    public static function generateCode(): string
    {
        return str_pad(random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
    }

    /**
     * Create a new OTP record
     */
    public static function createOtp(
        string $email,
        string $purpose = 'login',
        int $expiryMinutes = 10,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): array {
        // Generate OTP code
        $code = self::generateCode();
        
        // Invalidate any existing OTP codes for this email and purpose
        self::where('email', $email)
            ->where('purpose', $purpose)
            ->where('is_used', false)
            ->update(['is_used' => true]);

        // Create new OTP record
        $otpRecord = self::create([
            'email' => $email,
            'code_hash' => Hash::make($code),
            'purpose' => $purpose,
            'expires_at' => Carbon::now()->addMinutes($expiryMinutes),
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
        ]);

        return [
            'code' => $code,
            'record' => $otpRecord,
        ];
    }

    /**
     * Verify OTP code
     */
    public static function verifyOtp(
        string $email,
        string $code,
        string $purpose = 'login'
    ): array {
        // Find the most recent unused OTP for this email and purpose
        $otpRecord = self::where('email', $email)
            ->where('purpose', $purpose)
            ->where('is_used', false)
            ->where('expires_at', '>', Carbon::now())
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$otpRecord) {
            return [
                'success' => false,
                'message' => 'Invalid or expired OTP code',
                'error_code' => 'OTP_NOT_FOUND'
            ];
        }

        // Increment attempts
        $otpRecord->increment('attempts');

        // Check if too many attempts
        if ($otpRecord->attempts > 5) {
            $otpRecord->update(['is_used' => true]);
            return [
                'success' => false,
                'message' => 'Too many verification attempts. Please request a new OTP.',
                'error_code' => 'TOO_MANY_ATTEMPTS'
            ];
        }

        // Verify the code
        if (!Hash::check($code, $otpRecord->code_hash)) {
            return [
                'success' => false,
                'message' => 'Invalid OTP code',
                'error_code' => 'INVALID_CODE',
                'attempts_remaining' => 5 - $otpRecord->attempts
            ];
        }

        // Mark as used
        $otpRecord->update(['is_used' => true]);

        return [
            'success' => true,
            'message' => 'OTP verified successfully',
            'record' => $otpRecord
        ];
    }

    /**
     * Clean up expired OTP codes
     */
    public static function cleanupExpired(): int
    {
        return self::where('expires_at', '<', Carbon::now()->subHours(24))
            ->delete();
    }

    /**
     * Check if user can request new OTP (rate limiting)
     */
    public static function canRequestOtp(string $email, string $purpose = 'login'): array
    {
        $recentOtps = self::where('email', $email)
            ->where('purpose', $purpose)
            ->where('created_at', '>', Carbon::now()->subMinutes(1))
            ->count();

        if ($recentOtps >= 3) {
            return [
                'can_request' => false,
                'message' => 'Too many OTP requests. Please wait before requesting again.',
                'wait_time' => 60 // seconds
            ];
        }

        $recentOtpsHour = self::where('email', $email)
            ->where('purpose', $purpose)
            ->where('created_at', '>', Carbon::now()->subHour())
            ->count();

        if ($recentOtpsHour >= 10) {
            return [
                'can_request' => false,
                'message' => 'Hourly OTP limit exceeded. Please try again later.',
                'wait_time' => 3600 // seconds
            ];
        }

        return [
            'can_request' => true,
            'message' => 'OTP can be requested'
        ];
    }

    /**
     * Scope for active (non-expired, non-used) OTPs
     */
    public function scopeActive($query)
    {
        return $query->where('is_used', false)
            ->where('expires_at', '>', Carbon::now());
    }

    /**
     * Check if OTP is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Get time remaining until expiry
     */
    public function getTimeRemaining(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return $this->expires_at->diffInSeconds(Carbon::now());
    }
}
