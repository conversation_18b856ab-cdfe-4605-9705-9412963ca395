<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'description', 'price','vendor_id', 'category_id', 'image',
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function auction()
    {
        return $this->hasOne(Auction::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    public function getImagePathAttribute()
{
    return $this->image ? asset('storage/' . $this->image) : asset('images/default-product.jpg');
}
public function images()
{
    return $this->hasMany(ProductImage::class);
}

}
