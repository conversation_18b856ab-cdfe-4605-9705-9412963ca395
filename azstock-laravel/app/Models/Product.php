<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'description', 'price','vendor_id', 'category_id', 'image',
    ];

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function auction()
    {
        return $this->hasOne(Auction::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    public function getImagePathAttribute()
    {
        // If image is already a full URL (Vercel Blob), return as is
        if ($this->image && filter_var($this->image, FILTER_VALIDATE_URL)) {
            return $this->image;
        }

        // If it's a legacy local path, construct the URL
        if ($this->image) {
            // Check if it's a Vercel Blob path
            $baseUrl = config('services.vercel_blob.base_url');
            if ($baseUrl) {
                return rtrim($baseUrl, '/') . '/' . ltrim($this->image, '/');
            }
            // Fallback to local storage for backward compatibility
            return asset('storage/' . $this->image);
        }

        return asset('images/default-product.jpg');
    }
public function images()
{
    return $this->hasMany(ProductImage::class);
}

}
