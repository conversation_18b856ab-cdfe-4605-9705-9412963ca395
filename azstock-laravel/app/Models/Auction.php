<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Auction extends Model
{
    use HasFactory;
    protected $fillable = [
        'vendor_id',
        'product_id',
        'start_time',
        'end_time',
        'starting_price',
        'current_price',
        'reserve_price',
        'status',
        'description',
        'auction_type',
        'featured_image'
    ];
    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function products()
    {
        return $this->hasMany(Product::class);
    }

    public function vendor()
    {
        return $this->belongsTo(Vendor::class);
    }

    public function bids()
    {
        return $this->hasMany(Bid::class);
    }

    public function highestBid()
    {
        return $this->bids()->orderByDesc('bid_amount')->first();
    }

    /**
     * Get the winning bid for this auction.
     */
    public function winningBid()
    {
        return $this->bids()->where('status', 'won')->first();
    }

    /**
     * Check if the auction has ended.
     *
     * @return bool
     */
    public function hasEnded()
    {
        return now() > $this->end_time || $this->status === 'ended';
    }

    /**
     * Check if the auction has started.
     *
     * @return bool
     */
    public function hasStarted()
    {
        return now() >= $this->start_time;
    }

    /**
     * Check if the auction is active.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->status === 'active' && $this->hasStarted() && !$this->hasEnded();
    }

    /**
     * End the auction and process the results.
     *
     * @return bool
     */
    public function end()
    {
        if ($this->status !== 'active') {
            return false;
        }

        // Update auction status
        $this->status = 'ended';
        $this->save();

        // Get the highest bid
        $highestBid = $this->highestBid();

        // If there's a highest bid and it meets the reserve price (if set)
        if ($highestBid && ($this->reserve_price === null || $highestBid->bid_amount >= $this->reserve_price)) {
            // Mark the highest bid as won
            $highestBid->markAsWon();

            // Mark all other bids as lost and refund them
            $this->bids()
                ->where('id', '!=', $highestBid->id)
                ->where('status', 'active')
                ->get()
                ->each(function ($bid) use ($highestBid) {
                    $reason = "Outbid by winning bid #{$highestBid->id} with amount {$highestBid->bid_amount}";
                    $bid->markAsLost($reason);
                });

            return true;
        } else {
            // No winner, refund all bids
            $reason = $this->reserve_price
                ? "Auction ended with no winner: reserve price of {$this->reserve_price} not met"
                : "Auction ended with no bids";

            $this->bids()
                ->where('status', 'active')
                ->get()
                ->each(function ($bid) use ($reason) {
                    $bid->markAsLost($reason);
                });

            return true;
        }
    }

    /**
     * Get the featured image URL
     */
    public function getFeaturedImageUrl(): ?string
    {
        if (!$this->featured_image) {
            return null;
        }

        // If it's already a full URL (Vercel Blob), return as is
        if (filter_var($this->featured_image, FILTER_VALIDATE_URL)) {
            return $this->featured_image;
        }

        // Otherwise, it's a local storage path
        return asset('storage/' . $this->featured_image);
    }

    /**
     * Check if auction is sealed type
     */
    public function isSealed(): bool
    {
        return $this->auction_type === 'sealed';
    }

    /**
     * Check if auction is online type
     */
    public function isOnline(): bool
    {
        return $this->auction_type === 'online';
    }
}
