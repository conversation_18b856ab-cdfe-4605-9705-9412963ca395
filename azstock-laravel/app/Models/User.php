<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;


class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles;
    protected $guard_name = 'web';
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'roles',
        'is_verified_buyer',
        'is_verified_vendor',
        'is_verified_admin',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_verified_buyer' => 'boolean',
            'is_verified_vendor' => 'boolean',
            'is_verified_admin' => 'boolean',
        ];
    }

    /**
     * Check if the user is a vendor.
     *
     * @return bool
     */
    public function isVendor()
    {
        // Check both old role field and new Spatie roles for backward compatibility
        return $this->role === 'vendor' || $this->hasRole('vendor');
    }

    /**
     * Check if the user is an admin.
     *
     * @return bool
     */
    public function isAdmin()
    {
        // Check both old role field and new Spatie roles for backward compatibility
        return $this->role === 'admin' || $this->hasRole('admin');
    }

    /**
     * Check if the user is a buyer.
     *
     * @return bool
     */
    public function isBuyer()
    {
        // Check both old role field and new Spatie roles for backward compatibility
        return $this->role === 'buyer' || $this->hasRole('buyer');
    }

    /**
     * Get all user roles as an array.
     *
     * @return array
     */
    public function getUserRoles()
    {
        $roles = [];

        // Include old role field for backward compatibility
        if ($this->role) {
            $roles[] = $this->role;
        }

        // Include Spatie roles
        $spatieRoles = $this->roles->pluck('name')->toArray();
        $roles = array_merge($roles, $spatieRoles);

        return array_unique($roles);
    }

    /**
     * Check if user has any of the specified roles.
     *
     * @param array|string $roles
     * @return bool
     */
    public function hasAnyRole($roles)
    {
        if (is_string($roles)) {
            $roles = [$roles];
        }

        $userRoles = $this->getUserRoles();

        return !empty(array_intersect($roles, $userRoles));
    }

    /**
     * Add a role to the user (using Spatie).
     *
     * @param string $role
     * @return $this
     */
    public function addRole($role)
    {
        if (!$this->hasRole($role)) {
            $this->assignRole($role);
        }

        return $this;
    }

    /**
     * Remove a role from the user (using Spatie).
     *
     * @param string $role
     * @return $this
     */
    public function removeUserRole($role)
    {
        if ($this->hasRole($role)) {
            $this->removeRole($role);
        }

        return $this;
    }

    /**
     * Check if user is verified for buyer role.
     *
     * @return bool
     */
    public function isVerifiedBuyer()
    {
        return $this->is_verified_buyer;
    }

    /**
     * Check if user is verified for vendor role.
     *
     * @return bool
     */
    public function isVerifiedVendor()
    {
        return $this->is_verified_vendor;
    }

    /**
     * Check if user is verified for admin role.
     *
     * @return bool
     */
    public function isVerifiedAdmin()
    {
        return $this->is_verified_admin;
    }

    /**
     * Check if user can perform buyer actions (has buyer role AND is verified).
     *
     * @return bool
     */
    public function canBuy()
    {
        return $this->isBuyer() && $this->isVerifiedBuyer();
    }

    /**
     * Check if user can perform vendor actions (has vendor role AND is verified).
     *
     * @return bool
     */
    public function canSell()
    {
        return $this->isVendor() && $this->isVerifiedVendor();
    }

    /**
     * Check if user can perform admin actions (has admin role AND is verified).
     *
     * @return bool
     */
    public function canAdmin()
    {
        return $this->isAdmin() && $this->isVerifiedAdmin();
    }

    /**
     * Verify user for a specific role.
     *
     * @param string $role
     * @return $this
     */
    public function verifyForRole($role)
    {
        switch ($role) {
            case 'buyer':
                $this->is_verified_buyer = true;
                break;
            case 'vendor':
                $this->is_verified_vendor = true;
                break;
            case 'admin':
                $this->is_verified_admin = true;
                break;
        }

        $this->save();
        return $this;
    }

    /**
     * Unverify user for a specific role.
     *
     * @param string $role
     * @return $this
     */
    public function unverifyForRole($role)
    {
        switch ($role) {
            case 'buyer':
                $this->is_verified_buyer = false;
                break;
            case 'vendor':
                $this->is_verified_vendor = false;
                break;
            case 'admin':
                $this->is_verified_admin = false;
                break;
        }

        $this->save();
        return $this;
    }

    /**
     * Get verification status for all roles.
     *
     * @return array
     */
    public function getVerificationStatus()
    {
        return [
            'buyer' => $this->is_verified_buyer,
            'vendor' => $this->is_verified_vendor,
            'admin' => $this->is_verified_admin,
        ];
    }
    public function vendor()
    {
        return $this->hasOne(Vendor::class);
    }

    /**
     * Get the wallet associated with the user.
     */
    public function wallet()
    {
        return $this->hasOne(Wallet::class);
    }

    /**
     * Get the wallet transactions for the user.
     */
    public function walletTransactions()
    {
        return $this->hasMany(WalletTransaction::class);
    }

    /**
     * Get the wallet holds for the user.
     */
    public function walletHolds()
    {
        return $this->hasMany(WalletHold::class);
    }

    /**
     * Get or create a wallet for the user.
     *
     * @return \App\Models\Wallet
     */
    public function getWallet()
    {
        $wallet = $this->wallet;

        if (!$wallet) {
            $wallet = Wallet::create([
                'user_id' => $this->id,
                'balance' => 0,
                'held_balance' => 0,
                'available_balance' => 0,
            ]);
        }

        return $wallet;
    }

    /**
     * Deposit funds into the user's wallet.
     *
     * @param float $amount
     * @param string $description
     * @param array $metadata
     * @return \App\Models\WalletTransaction
     */
    public function deposit($amount, $description = 'Deposit', $metadata = [])
    {
        $wallet = $this->getWallet();
        return $wallet->deposit($amount, $description, $metadata);
    }

    /**
     * Withdraw funds from the user's wallet.
     *
     * @param float $amount
     * @param string $description
     * @param array $metadata
     * @return \App\Models\WalletTransaction|false
     */
    public function withdraw($amount, $description = 'Withdrawal', $metadata = [])
    {
        $wallet = $this->getWallet();
        return $wallet->withdraw($amount, $description, $metadata);
    }

    /**
     * Get the user's wallet balance.
     *
     * @return float
     */
    public function getBalance()
    {
        $wallet = $this->wallet;
        return $wallet ? $wallet->balance : 0;
    }

    /**
     * Get the user's available wallet balance.
     *
     * @return float
     */
    public function getAvailableBalance()
    {
        $wallet = $this->wallet;
        return $wallet ? $wallet->available_balance : 0;
    }

    /**
     * Get the user's held wallet balance.
     *
     * @return float
     */
    public function getHeldBalance()
    {
        $wallet = $this->wallet;
        return $wallet ? $wallet->held_balance : 0;
    }
}
