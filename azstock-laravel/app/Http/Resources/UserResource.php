<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->role, // Primary role for backward compatibility
            'roles' => $this->getUserRoles(), // All user roles
            'email_verified_at' => $this->email_verified_at,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'vendor' => $this->when($this->relationLoaded('vendor') || $this->isVendor(),
                $this->vendor ? new VendorResource($this->vendor) : null),
            'wallet' => $this->when($this->relationLoaded('wallet'),
                $this->wallet ? new WalletResource($this->wallet) : null),
            'spatie_roles' => $this->when($this->relationLoaded('roles'),
                $this->roles->pluck('name')->toArray()),
            'is_vendor' => $this->isVendor(),
            'is_admin' => $this->isAdmin(),
            'is_buyer' => $this->isBuyer(),
            'verification_status' => [
                'buyer' => $this->is_verified_buyer,
                'vendor' => $this->is_verified_vendor,
                'admin' => $this->is_verified_admin,
            ],
            'is_verified_buyer' => $this->is_verified_buyer,
            'is_verified_vendor' => $this->is_verified_vendor,
            'is_verified_admin' => $this->is_verified_admin,
            'can_buy' => $this->canBuy(), // Has buyer role AND is verified
            'can_sell' => $this->canSell(), // Has vendor role AND is verified
            'can_admin' => $this->canAdmin(), // Has admin role AND is verified
        ];
    }
}
