<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Generate thumbnail URL
        $pathInfo = pathinfo($this->image_path);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
        $thumbnailUrl = null;

        if (\Illuminate\Support\Facades\Storage::disk('public')->exists($thumbnailPath)) {
            $thumbnailUrl = asset('storage/' . $thumbnailPath);
        }

        return [
            'id' => $this->id,
            'url' => asset('storage/' . $this->image_path),
            'thumbnail_url' => $thumbnailUrl,
            'original_name' => $this->original_name ?? null,
            'file_size' => $this->file_size ?? null,
            'mime_type' => $this->mime_type ?? null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
