<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ProductImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Handle both Vercel Blob URLs and legacy local paths
        $imageUrl = $this->image_path;
        $thumbnailUrl = null;

        // If it's already a full URL (Vercel Blob), use as is
        if (filter_var($this->image_path, FILTER_VALIDATE_URL)) {
            $imageUrl = $this->image_path;
            // Generate thumbnail URL for Vercel Blob
            $parsedUrl = parse_url($this->image_path);
            if ($parsedUrl) {
                $pathInfo = pathinfo($parsedUrl['path']);
                $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
                $thumbnailUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $thumbnailPath;
            }
        } else {
            // Legacy local storage handling
            $baseUrl = config('services.vercel_blob.base_url');
            if ($baseUrl) {
                // Construct Vercel Blob URL
                $imageUrl = rtrim($baseUrl, '/') . '/' . ltrim($this->image_path, '/');
                // Generate thumbnail URL
                $pathInfo = pathinfo($this->image_path);
                $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
                $thumbnailUrl = rtrim($baseUrl, '/') . '/' . ltrim($thumbnailPath, '/');
            } else {
                // Fallback to local storage
                $imageUrl = asset('storage/' . $this->image_path);
                $pathInfo = pathinfo($this->image_path);
                $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
                if (\Illuminate\Support\Facades\Storage::disk('public')->exists($thumbnailPath)) {
                    $thumbnailUrl = asset('storage/' . $thumbnailPath);
                }
            }
        }

        return [
            'id' => $this->id,
            'url' => $imageUrl,
            'thumbnail_url' => $thumbnailUrl,
            'original_name' => $this->original_name ?? null,
            'file_size' => $this->file_size ?? null,
            'mime_type' => $this->mime_type ?? null,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
