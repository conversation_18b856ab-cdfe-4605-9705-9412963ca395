<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class DebugAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Only debug in development environment
        if (config('app.debug')) {
            $bearerToken = $request->bearerToken();
            
            Log::info('DebugAuth Middleware - Request Details', [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'headers' => [
                    'authorization' => $request->header('Authorization'),
                    'accept' => $request->header('Accept'),
                    'content-type' => $request->header('Content-Type'),
                    'user-agent' => $request->header('User-Agent'),
                ],
                'bearer_token_present' => $bearerToken ? 'yes' : 'no',
                'bearer_token_length' => $bearerToken ? strlen($bearerToken) : 0,
                'bearer_token_prefix' => $bearerToken ? substr($bearerToken, 0, 10) . '...' : null,
            ]);

            // Check if token exists in database
            if ($bearerToken) {
                $tokenModel = PersonalAccessToken::findToken($bearerToken);
                
                Log::info('DebugAuth Middleware - Token Validation', [
                    'token_found_in_db' => $tokenModel ? 'yes' : 'no',
                    'token_id' => $tokenModel ? $tokenModel->id : null,
                    'token_name' => $tokenModel ? $tokenModel->name : null,
                    'token_user_id' => $tokenModel ? $tokenModel->tokenable_id : null,
                    'token_created_at' => $tokenModel ? $tokenModel->created_at : null,
                    'token_last_used_at' => $tokenModel ? $tokenModel->last_used_at : null,
                    'token_expires_at' => $tokenModel ? $tokenModel->expires_at : null,
                ]);

                // Check if user exists
                if ($tokenModel && $tokenModel->tokenable) {
                    $user = $tokenModel->tokenable;
                    Log::info('DebugAuth Middleware - User Details', [
                        'user_id' => $user->id,
                        'user_email' => $user->email,
                        'user_name' => $user->name,
                        'user_created_at' => $user->created_at,
                    ]);
                }
            }
        }

        $response = $next($request);

        // Log response status if debugging
        if (config('app.debug')) {
            Log::info('DebugAuth Middleware - Response', [
                'status_code' => $response->getStatusCode(),
                'authenticated_user' => auth()->check() ? auth()->user()->id : 'none',
            ]);
        }

        return $response;
    }
}
