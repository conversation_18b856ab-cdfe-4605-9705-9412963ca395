<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Services\ActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Validation\ValidationException;

class AuthController extends Controller
{
    /**
     * Register a new user
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function register(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            // 'role' => 'sometimes|string|in:buyer,vendor', // Removed admin from public registration
            'roles' => 'sometimes|array', // Multi-role support
            'roles.*' => 'string|in:buyer,vendor', // Removed admin from public registration
        ]);

        // Determine roles to assign
        $rolesToAssign = [];
        if ($request->has('roles') && is_array($request->roles)) {
            $rolesToAssign = $request->roles;
        } elseif ($request->has('role')) {
            $rolesToAssign = [$request->role];
        } else {
            $rolesToAssign = ['buyer']; // Default role
        }

        // Check for admin role restriction
        $this->validateAdminRegistration($rolesToAssign);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $rolesToAssign[0], // Keep first role for backward compatibility
            // New users are unverified by default
            'is_verified_buyer' => false,
            'is_verified_vendor' => false,
            'is_verified_admin' => false,
        ]);

        // Assign Spatie roles for multi-role support
        foreach ($rolesToAssign as $role) {
            $user->assignRole($role);
        }

        // If user has vendor role, create a vendor profile
        if (in_array('vendor', $rolesToAssign)) {
            $user->vendor()->create([
                'name' => $request->name,
                'slug' => \Illuminate\Support\Str::slug($request->name . '-' . $user->id),
                'email' => $request->email,
                'is_active' => true,
            ]);
        }

        // For now, we'll use a simple token generation
        $token = $user->createToken('auth_token')->plainTextToken;

        return (new UserResource($user))
            ->additional([
                'message' => 'User registered successfully',
                'token' => $token,
            ])
            ->response()
            ->setStatusCode(201);
    }

    /**
     * Login user and create token
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if (!Auth::attempt($request->only('email', 'password'))) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect.'],
            ]);
        }

        $user = $request->user();
        $token = $user->createToken('auth_token')->plainTextToken;

        // Log the login activity
        ActivityLogger::logLogin($user->id);

        return (new UserResource($user))
            ->additional([
                'message' => 'Login successful',
                'token' => $token,
            ]);
    }

    /**
     * Refresh the user's token and return updated user data
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function refreshToken(Request $request)
    {
        try {
            $user = $request->user();

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            // Get the current token
            $currentToken = $request->user()->currentAccessToken();

            if (!$currentToken) {
                return response()->json([
                    'success' => false,
                    'message' => 'No valid token found'
                ], 401);
            }

            // Create a new token
            $newToken = $user->createToken('auth_token')->plainTextToken;

            // Optionally revoke the old token (uncomment if you want single-session behavior)
            // $currentToken->delete();

            // Load fresh user data with relationships
            $user->load(['vendor', 'wallet', 'roles']);

            \Log::info('Token refreshed successfully', [
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Token refreshed successfully',
                'data' => new UserResource($user),
                'token' => $newToken,
            ]);

        } catch (\Exception $e) {
            \Log::error('Token refresh failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Token refresh failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Logout user (Revoke the token)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logout(Request $request)
    {
        $user = $request->user();

        // Log the logout activity
        ActivityLogger::logLogout($user->id);

        // Delete all tokens
        $user->tokens()->delete();

        return response()->json([
            'message' => 'Successfully logged out'
        ]);
    }

    /**
     * Validate admin registration restrictions.
     *
     * @param array $rolesToAssign
     * @throws \Illuminate\Validation\ValidationException
     */
    private function validateAdminRegistration($rolesToAssign)
    {
        // Check if trying to register as admin
        if (in_array('admin', $rolesToAssign)) {
            // Count existing admin users
            $adminCount = User::where('role', 'admin')
                ->orWhereHas('roles', function ($query) {
                    $query->where('name', 'admin');
                })
                ->count();

            // Only allow admin registration if no admins exist (bootstrap scenario)
            if ($adminCount > 0) {
                throw ValidationException::withMessages([
                    'role' => ['Admin registration is restricted. Admin users can only be created by existing admins.'],
                ]);
            }
        }
    }
}
