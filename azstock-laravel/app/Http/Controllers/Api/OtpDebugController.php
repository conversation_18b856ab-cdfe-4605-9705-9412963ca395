<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\OtpCode;
use App\Models\User;
use App\Services\OtpService;
use App\Mail\OtpCodeMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;

class OtpDebugController extends Controller
{
    /**
     * Debug OTP system status
     */
    public function debugStatus(Request $request)
    {
        try {
            $debug = [];

            // 1. Check Environment Configuration
            $debug['environment'] = [
                'app_key_set' => config('app.key') ? true : false,
                'mail_driver' => config('mail.default'),
                'sendgrid_configured' => config('mail.mailers.sendgrid.password') ? true : false,
                'mail_from_address' => config('mail.from.address'),
                'mail_from_name' => config('mail.from.name'),
                'database_connection' => config('database.default'),
                'queue_connection' => config('queue.default'),
            ];

            // 2. Test Database Connection
            try {
                DB::connection()->getPdo();
                $debug['database'] = [
                    'connection' => 'success',
                    'driver' => DB::connection()->getDriverName(),
                ];

                // Check if otp_codes table exists
                try {
                    $tableExists = DB::getSchemaBuilder()->hasTable('otp_codes');
                    $debug['database']['otp_table_exists'] = $tableExists;

                    if ($tableExists) {
                        $debug['database']['otp_records_count'] = OtpCode::count();
                        $debug['database']['recent_otps'] = OtpCode::orderBy('created_at', 'desc')
                            ->limit(5)
                            ->select(['id', 'email', 'purpose', 'is_used', 'expires_at', 'created_at'])
                            ->get();
                    }
                } catch (\Exception $e) {
                    $debug['database']['table_check_error'] = $e->getMessage();
                }
            } catch (\Exception $e) {
                $debug['database'] = [
                    'connection' => 'failed',
                    'error' => $e->getMessage(),
                ];
            }

            // 3. Test User Model
            try {
                $userCount = User::count();
                $debug['users'] = [
                    'total_users' => $userCount,
                    'test_user_exists' => User::where('email', '<EMAIL>')->exists(),
                ];
            } catch (\Exception $e) {
                $debug['users'] = [
                    'error' => $e->getMessage(),
                ];
            }

            // 4. Test Mail Configuration
            $debug['mail_config'] = [
                'default_mailer' => config('mail.default'),
                'sendgrid_host' => config('mail.mailers.sendgrid.host'),
                'sendgrid_port' => config('mail.mailers.sendgrid.port'),
                'sendgrid_username' => config('mail.mailers.sendgrid.username'),
                'sendgrid_password_length' => strlen(config('mail.mailers.sendgrid.password') ?? ''),
            ];

            // 5. Test OTP Generation (without sending email)
            try {
                $testEmail = '<EMAIL>';
                
                // Create test user if doesn't exist
                $testUser = User::firstOrCreate(
                    ['email' => $testEmail],
                    [
                        'name' => 'Debug Test User',
                        'password' => bcrypt('password'),
                        'email_verified_at' => now(),
                    ]
                );

                $otpData = OtpCode::createOtp($testEmail, 'login', 10);
                $debug['otp_generation'] = [
                    'success' => true,
                    'otp_id' => $otpData['record']->id,
                    'expires_at' => $otpData['record']->expires_at,
                    'code_length' => strlen($otpData['code']),
                ];
            } catch (\Exception $e) {
                $debug['otp_generation'] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'OTP system debug information',
                'debug' => $debug,
                'timestamp' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            Log::error('OTP Debug Status Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Debug check failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test email sending with log driver
     */
    public function testEmail(Request $request)
    {
        try {
            $email = $request->get('email', '<EMAIL>');
            
            // Temporarily switch to log driver for testing
            $originalDriver = config('mail.default');
            Config::set('mail.default', 'log');

            $testCode = '123456';
            $mail = new OtpCodeMail($testCode, 'Debug Test User', 'login', 10);

            Mail::to($email)->send($mail);

            // Restore original driver
            Config::set('mail.default', $originalDriver);

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully (logged to storage/logs/laravel.log)',
                'email' => $email,
                'test_code' => $testCode,
            ]);

        } catch (\Exception $e) {
            Log::error('OTP Test Email Error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test email failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test actual email sending with SendGrid
     */
    public function testRealEmail(Request $request)
    {
        try {
            $email = $request->get('email');
            
            if (!$email) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email address is required',
                ], 400);
            }

            // Validate email format
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid email format',
                ], 400);
            }

            $testCode = '123456';
            $mail = new OtpCodeMail($testCode, 'Debug Test User', 'login', 10);

            Mail::to($email)->send($mail);

            return response()->json([
                'success' => true,
                'message' => 'Real email sent successfully via SendGrid',
                'email' => $email,
                'test_code' => $testCode,
                'note' => 'Check your email inbox (and spam folder)',
            ]);

        } catch (\Exception $e) {
            Log::error('OTP Real Email Test Error', [
                'email' => $request->get('email'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Real email sending failed',
                'error' => $e->getMessage(),
                'details' => 'Check Laravel logs for more information',
            ], 500);
        }
    }

    /**
     * Clean up debug data
     */
    public function cleanup(Request $request)
    {
        try {
            $deleted = OtpCode::where('email', '<EMAIL>')->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Debug data cleaned up',
                'deleted_records' => $deleted,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cleanup failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
