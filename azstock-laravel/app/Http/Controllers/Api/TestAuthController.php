<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\PersonalAccessToken;

class TestAuthController extends Controller
{
    /**
     * Test authentication endpoint for debugging
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function testAuth(Request $request)
    {
        try {
            $bearerToken = $request->bearerToken();
            $user = $request->user();
            
            $debugInfo = [
                'timestamp' => now()->toISOString(),
                'request_info' => [
                    'method' => $request->method(),
                    'url' => $request->fullUrl(),
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ],
                'headers' => [
                    'authorization' => $request->header('Authorization'),
                    'accept' => $request->header('Accept'),
                    'content_type' => $request->header('Content-Type'),
                ],
                'token_info' => [
                    'bearer_token_present' => $bearerToken ? true : false,
                    'bearer_token_length' => $bearerToken ? strlen($bearerToken) : 0,
                    'bearer_token_prefix' => $bearerToken ? substr($bearerToken, 0, 10) . '...' : null,
                ],
                'authentication_status' => [
                    'is_authenticated' => auth()->check(),
                    'auth_guard' => auth()->getDefaultDriver(),
                    'user_found' => $user ? true : false,
                ],
            ];

            // Check token in database
            if ($bearerToken) {
                $tokenModel = PersonalAccessToken::findToken($bearerToken);
                $debugInfo['token_validation'] = [
                    'token_found_in_db' => $tokenModel ? true : false,
                    'token_id' => $tokenModel ? $tokenModel->id : null,
                    'token_name' => $tokenModel ? $tokenModel->name : null,
                    'token_user_id' => $tokenModel ? $tokenModel->tokenable_id : null,
                    'token_created_at' => $tokenModel ? $tokenModel->created_at->toISOString() : null,
                    'token_last_used_at' => $tokenModel ? ($tokenModel->last_used_at ? $tokenModel->last_used_at->toISOString() : null) : null,
                    'token_expires_at' => $tokenModel ? ($tokenModel->expires_at ? $tokenModel->expires_at->toISOString() : null) : null,
                ];

                if ($tokenModel && $tokenModel->tokenable) {
                    $tokenUser = $tokenModel->tokenable;
                    $debugInfo['token_user'] = [
                        'id' => $tokenUser->id,
                        'email' => $tokenUser->email,
                        'name' => $tokenUser->name,
                        'created_at' => $tokenUser->created_at->toISOString(),
                    ];
                }
            }

            // User info
            if ($user) {
                $debugInfo['user_info'] = [
                    'id' => $user->id,
                    'email' => $user->email,
                    'name' => $user->name,
                    'role' => $user->role,
                    'roles' => $user->getUserRoles(),
                    'verification_status' => [
                        'buyer' => $user->is_verified_buyer,
                        'vendor' => $user->is_verified_vendor,
                        'admin' => $user->is_verified_admin,
                    ],
                    'created_at' => $user->created_at->toISOString(),
                ];
            }

            Log::info('TestAuth endpoint called', $debugInfo);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'Not authenticated',
                    'debug_info' => $debugInfo,
                ], 401);
            }

            return response()->json([
                'success' => true,
                'message' => 'Authentication successful',
                'user' => [
                    'id' => $user->id,
                    'email' => $user->email,
                    'name' => $user->name,
                    'role' => $user->role,
                    'roles' => $user->getUserRoles(),
                    'verification_status' => [
                        'buyer' => $user->is_verified_buyer,
                        'vendor' => $user->is_verified_vendor,
                        'admin' => $user->is_verified_admin,
                    ],
                ],
                'debug_info' => $debugInfo,
            ]);

        } catch (\Exception $e) {
            Log::error('TestAuth endpoint error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test auth endpoint error',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test endpoint without authentication (for comparison)
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function testPublic(Request $request)
    {
        return response()->json([
            'success' => true,
            'message' => 'Public endpoint working',
            'timestamp' => now()->toISOString(),
            'request_info' => [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
            ],
        ]);
    }
}
