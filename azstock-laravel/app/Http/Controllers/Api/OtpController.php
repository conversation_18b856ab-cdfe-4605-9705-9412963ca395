<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\OtpService;
use App\Http\Resources\UserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\RateLimiter;

class OtpController extends Controller
{
    protected OtpService $otpService;

    public function __construct(OtpService $otpService)
    {
        $this->otpService = $otpService;
    }

    /**
     * Request OTP code
     */
    public function requestOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'purpose' => 'sometimes|string|in:login,password_reset,email_verification',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $email = $request->email;
        $purpose = $request->get('purpose', 'login');

        // Rate limiting per IP
        $key = 'otp-request:' . $request->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => 'Too many OTP requests from this IP. Please try again later.',
                'error_code' => 'IP_RATE_LIMITED',
                'retry_after' => $seconds
            ], 429);
        }

        RateLimiter::hit($key, 300); // 5 minutes

        // Send OTP
        $result = $this->otpService->sendOtp(
            $email,
            $purpose,
            10, // 10 minutes expiry
            $request->ip(),
            $request->userAgent()
        );

        $statusCode = $result['success'] ? 200 : 400;

        return response()->json($result, $statusCode);
    }

    /**
     * Verify OTP code
     */
    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'code' => 'required|string|size:6|regex:/^[0-9]{6}$/',
            'purpose' => 'sometimes|string|in:login,password_reset,email_verification',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $email = $request->email;
        $code = $request->code;
        $purpose = $request->get('purpose', 'login');

        // Rate limiting per email for verification attempts
        $key = 'otp-verify:' . $email;
        if (RateLimiter::tooManyAttempts($key, 10)) {
            $seconds = RateLimiter::availableIn($key);
            return response()->json([
                'success' => false,
                'message' => 'Too many verification attempts. Please try again later.',
                'error_code' => 'VERIFY_RATE_LIMITED',
                'retry_after' => $seconds
            ], 429);
        }

        RateLimiter::hit($key, 300); // 5 minutes

        // Verify OTP
        $result = $this->otpService->verifyOtp($email, $code, $purpose);

        if ($result['success']) {
            // Clear rate limiting on successful verification
            RateLimiter::clear($key);

            // For login purpose, return user data and token
            if ($purpose === 'login' && isset($result['user'])) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => new UserResource($result['user']),
                    'token' => $result['token'],
                ]);
            }

            // For other purposes, just return success
            return response()->json([
                'success' => true,
                'message' => $result['message'],
            ]);
        }

        $statusCode = match($result['error_code'] ?? '') {
            'TOO_MANY_ATTEMPTS' => 429,
            'OTP_NOT_FOUND', 'INVALID_CODE' => 400,
            default => 400
        };

        return response()->json($result, $statusCode);
    }

    /**
     * Resend OTP code
     */
    public function resendOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'purpose' => 'sometimes|string|in:login,password_reset,email_verification',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Use the same logic as requestOtp
        return $this->requestOtp($request);
    }

    /**
     * Get OTP status for debugging (admin only)
     */
    public function getOtpStatus(Request $request)
    {
        // Check if user is admin
        $user = $request->user();
        if (!$user || !$user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'sometimes|email|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $email = $request->get('email');
        $stats = $this->otpService->getOtpStats($email);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Revoke user OTPs (admin only)
     */
    public function revokeUserOtps(Request $request)
    {
        // Check if user is admin
        $user = $request->user();
        if (!$user || !$user->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Admin access required.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|max:255',
            'purpose' => 'sometimes|string|in:login,password_reset,email_verification',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $email = $request->email;
        $purpose = $request->get('purpose');

        $revokedCount = $this->otpService->revokeUserOtps($email, $purpose);

        return response()->json([
            'success' => true,
            'message' => "Revoked {$revokedCount} OTP codes",
            'revoked_count' => $revokedCount,
        ]);
    }
}
