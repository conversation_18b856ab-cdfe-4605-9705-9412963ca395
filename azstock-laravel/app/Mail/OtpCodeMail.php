<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class OtpCodeMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public string $otpCode;
    public string $userName;
    public string $purpose;
    public int $expiryMinutes;

    /**
     * Create a new message instance.
     */
    public function __construct(
        string $otpCode,
        string $userName = '',
        string $purpose = 'login',
        int $expiryMinutes = 10
    ) {
        $this->otpCode = $otpCode;
        $this->userName = $userName;
        $this->purpose = $purpose;
        $this->expiryMinutes = $expiryMinutes;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = match($this->purpose) {
            'login' => 'Your Login Verification Code',
            'password_reset' => 'Password Reset Verification Code',
            'email_verification' => 'Email Verification Code',
            default => 'Your Verification Code'
        };

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            html: 'emails.otp-code',
            text: 'emails.otp-code-text',
            with: [
                'otpCode' => $this->otpCode,
                'userName' => $this->userName,
                'purpose' => $this->purpose,
                'expiryMinutes' => $this->expiryMinutes,
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
