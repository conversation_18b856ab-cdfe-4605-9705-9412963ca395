<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class VercelBlobService
{
    private Client $client;
    private string $baseUrl;
    private string $token;

    public function __construct()
    {
        $this->client = new Client();
        $this->baseUrl = config('services.vercel_blob.base_url');
        $this->token = config('services.vercel_blob.token');
    }

    /**
     * Upload a file to Vercel Blob Storage
     *
     * @param UploadedFile $file
     * @param string $filename
     * @return array
     * @throws \Exception
     */
    public function uploadFile(UploadedFile $file, string $filename): array
    {
        try {
            // Vercel Blob Storage API endpoint
            $apiUrl = 'https://blob.vercel-storage.com';

            $response = $this->client->post($apiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'multipart/form-data',
                ],
                'multipart' => [
                    [
                        'name' => 'file',
                        'contents' => $file->getContent(),
                        'filename' => $filename,
                        'headers' => [
                            'Content-Type' => $file->getMimeType(),
                        ]
                    ]
                ],
                'query' => [
                    'filename' => $filename,
                ]
            ]);

            $responseData = json_decode($response->getBody()->getContents(), true);

            if (!isset($responseData['url'])) {
                throw new \Exception('Invalid response from Vercel Blob API');
            }

            return [
                'success' => true,
                'url' => $responseData['url'],
                'filename' => $filename,
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
            ];
        } catch (RequestException $e) {
            Log::error('Vercel Blob upload failed', [
                'filename' => $filename,
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null,
            ]);

            throw new \Exception('Failed to upload file to Vercel Blob: ' . $e->getMessage());
        }
    }

    /**
     * Delete a file from Vercel Blob Storage
     *
     * @param string $url
     * @return bool
     */
    public function deleteFile(string $url): bool
    {
        try {
            // Vercel Blob Storage delete API endpoint
            $apiUrl = 'https://blob.vercel-storage.com/delete';

            $response = $this->client->post($apiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'urls' => [$url]
                ]
            ]);

            return $response->getStatusCode() === 200;
        } catch (RequestException $e) {
            Log::error('Vercel Blob delete failed', [
                'url' => $url,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Generate a unique filename for upload
     *
     * @param UploadedFile $file
     * @param string $directory
     * @return string
     */
    public function generateFilename(UploadedFile $file, string $directory = ''): string
    {
        $extension = $file->getClientOriginalExtension();
        $filename = uniqid() . '_' . time() . '.' . $extension;
        
        return $directory ? $directory . '/' . $filename : $filename;
    }

    /**
     * Check if Vercel Blob is properly configured
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        return !empty($this->baseUrl) && !empty($this->token);
    }
}
