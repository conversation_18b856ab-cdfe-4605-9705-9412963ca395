<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageService
{
    /**
     * Upload and process an image file.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param array $options
     * @return array
     */
    public function uploadImage(UploadedFile $file, string $directory, array $options = []): array
    {
        // Validate file
        $this->validateImage($file);

        // Generate unique filename
        $filename = $this->generateFilename($file);

        // Create full path
        $path = $directory . '/' . $filename;

        // Store the file
        $storedPath = Storage::disk('public')->putFileAs($directory, $file, $filename);

        if (!$storedPath) {
            throw new \Exception('Failed to store image file');
        }

        // Get file info
        $fileInfo = [
            'original_name' => $file->getClientOriginalName(),
            'filename' => $filename,
            'path' => $storedPath,
            'url' => Storage::disk('public')->url($storedPath),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
        ];

        // Create thumbnail if requested
        if (isset($options['create_thumbnail']) && $options['create_thumbnail']) {
            $thumbnailInfo = $this->createThumbnail($storedPath, $options);
            $fileInfo['thumbnail'] = $thumbnailInfo;
        }

        return $fileInfo;
    }
    
    /**
     * Upload multiple images.
     *
     * @param array $files
     * @param string $directory
     * @param array $options
     * @return array
     */
    public function uploadMultipleImages(array $files, string $directory, array $options = []): array
    {
        $uploadedFiles = [];
        $errors = [];
        
        foreach ($files as $index => $file) {
            try {
                $uploadedFiles[] = $this->uploadImage($file, $directory, $options);
            } catch (\Exception $e) {
                $errors[] = [
                    'index' => $index,
                    'filename' => $file->getClientOriginalName(),
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return [
            'uploaded' => $uploadedFiles,
            'errors' => $errors
        ];
    }
    
    /**
     * Delete an image file.
     *
     * @param string $path
     * @return bool
     */
    public function deleteImage(string $path): bool
    {
        if (Storage::disk('public')->exists($path)) {
            return Storage::disk('public')->delete($path);
        }

        return true; // Consider it successful if file doesn't exist
    }
    
    /**
     * Delete multiple images.
     *
     * @param array $paths
     * @return array
     */
    public function deleteMultipleImages(array $paths): array
    {
        $deleted = [];
        $errors = [];
        
        foreach ($paths as $path) {
            try {
                if ($this->deleteImage($path)) {
                    $deleted[] = $path;
                } else {
                    $errors[] = $path;
                }
            } catch (\Exception $e) {
                $errors[] = $path;
            }
        }
        
        return [
            'deleted' => $deleted,
            'errors' => $errors
        ];
    }
    
    /**
     * Create a thumbnail for an image.
     *
     * @param string $imagePath
     * @param array $options
     * @return array
     */
    public function createThumbnail(string $imagePath, array $options = []): array
    {
        $width = $options['thumbnail_width'] ?? 300;
        $height = $options['thumbnail_height'] ?? 300;

        $fullPath = Storage::disk('public')->path($imagePath);
        $pathInfo = pathinfo($imagePath);

        $thumbnailFilename = $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
        $thumbnailPath = $pathInfo['dirname'] . '/' . $thumbnailFilename;
        $thumbnailFullPath = Storage::disk('public')->path($thumbnailPath);

        // Create thumbnail using intervention/image if available
        try {
            $manager = new ImageManager(new Driver());
            $image = $manager->read($fullPath);
            $image->cover($width, $height);
            $image->save($thumbnailFullPath);
        } catch (\Exception $e) {
            // Fallback: copy original file as thumbnail
            Storage::disk('public')->copy($imagePath, $thumbnailPath);
        }

        return [
            'path' => $thumbnailPath,
            'url' => Storage::disk('public')->url($thumbnailPath),
            'width' => $width,
            'height' => $height
        ];
    }
    
    /**
     * Validate uploaded image file.
     *
     * @param UploadedFile $file
     * @throws \Exception
     */
    private function validateImage(UploadedFile $file): void
    {
        // Check if file is valid
        if (!$file->isValid()) {
            throw new \Exception('Invalid file upload');
        }
        
        // Check file size (max 5MB)
        $maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if ($file->getSize() > $maxSize) {
            throw new \Exception('File size exceeds maximum limit of 5MB');
        }
        
        // Check file type
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed');
        }
        
        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedExtensions)) {
            throw new \Exception('Invalid file extension');
        }
    }
    
    /**
     * Generate a unique filename for the uploaded file.
     *
     * @param UploadedFile $file
     * @return string
     */
    private function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        
        return $timestamp . '_' . $random . '.' . $extension;
    }
    
    /**
     * Get image URL from path.
     *
     * @param string $path
     * @return string
     */
    public function getImageUrl(string $path): string
    {
        return Storage::disk('public')->url($path);
    }

    /**
     * Check if image exists.
     *
     * @param string $path
     * @return bool
     */
    public function imageExists(string $path): bool
    {
        return Storage::disk('public')->exists($path);
    }

    /**
     * Move image from one location to another.
     *
     * @param string $sourcePath
     * @param string $destinationPath
     * @return bool
     */
    public function moveImage(string $sourcePath, string $destinationPath): bool
    {
        if (!Storage::disk('public')->exists($sourcePath)) {
            return false;
        }

        // Ensure destination directory exists
        $destinationDir = dirname($destinationPath);
        if (!Storage::disk('public')->exists($destinationDir)) {
            Storage::disk('public')->makeDirectory($destinationDir);
        }

        return Storage::disk('public')->move($sourcePath, $destinationPath);
    }



    /**
     * Move images from temporary storage to product storage.
     *
     * @param array $imageUrls
     * @param int $productId
     * @return array
     */
    public function moveImagesToProduct(array $imageUrls, int $productId): array
    {
        $movedImages = [];
        $errors = [];

        foreach ($imageUrls as $url) {
            try {
                // Extract path from URL
                $path = $this->extractPathFromUrl($url);

                if (!$path) {
                    $errors[] = "Could not extract path from URL: {$url}";
                    continue;
                }

                if (!$this->imageExists($path)) {
                    $errors[] = "Image file not found at path: {$path} (from URL: {$url})";
                    continue;
                }

                // Generate new path in product directory
                $filename = basename($path);
                $newPath = "products/{$productId}/{$filename}";

                // Move main image
                $moveResult = $this->moveImage($path, $newPath);
                \Log::info("Moving image from {$path} to {$newPath}: " . ($moveResult ? 'SUCCESS' : 'FAILED'));

                if ($moveResult) {
                    // Move thumbnail if exists
                    $thumbnailPath = $this->getThumbnailPathFromOriginal($path);
                    if ($thumbnailPath && $this->imageExists($thumbnailPath)) {
                        $newThumbnailPath = $this->getThumbnailPathFromOriginal($newPath);
                        $thumbnailMoveResult = $this->moveImage($thumbnailPath, $newThumbnailPath);
                        \Log::info("Moving thumbnail from {$thumbnailPath} to {$newThumbnailPath}: " . ($thumbnailMoveResult ? 'SUCCESS' : 'FAILED'));
                    }

                    $movedImages[] = [
                        'old_path' => $path,
                        'new_path' => $newPath,
                        'url' => $this->getImageUrl($newPath),
                        'thumbnail_url' => $this->imageExists($this->getThumbnailPathFromOriginal($newPath))
                            ? $this->getImageUrl($this->getThumbnailPathFromOriginal($newPath))
                            : null,
                    ];
                } else {
                    $errors[] = "Failed to move image from {$path} to {$newPath}";
                }
            } catch (\Exception $e) {
                $errors[] = "Error processing image {$url}: " . $e->getMessage();
            }
        }

        return [
            'moved' => $movedImages,
            'errors' => $errors
        ];
    }

    /**
     * Extract storage path from URL.
     *
     * @param string $url
     * @return string|null
     */
    private function extractPathFromUrl(string $url): ?string
    {
        \Log::info('Extracting path from URL', ['url' => $url]);

        // Try multiple approaches to extract the path

        // Method 1: Use Laravel's storage URL
        $storageUrl = Storage::disk('public')->url('');
        \Log::info('Storage URL check', ['storage_url' => $storageUrl, 'url_starts_with' => strpos($url, $storageUrl) === 0]);

        if (strpos($url, $storageUrl) === 0) {
            $extracted = substr($url, strlen($storageUrl));
            // Remove leading slash if present
            $extracted = ltrim($extracted, '/');
            \Log::info('Method 1 extracted', ['path' => $extracted]);
            return $extracted;
        }

        // Method 2: Look for /storage/ pattern (more flexible)
        if (preg_match('/\/storage\/(.+)$/', $url, $matches)) {
            \Log::info('Method 2 extracted', ['path' => $matches[1]]);
            return $matches[1];
        }

        // Method 3: Handle different localhost ports or domains
        if (preg_match('/^https?:\/\/[^\/]+\/storage\/(.+)$/', $url, $matches)) {
            \Log::info('Method 3 extracted', ['path' => $matches[1]]);
            return $matches[1];
        }

        \Log::error('Failed to extract path from URL', ['url' => $url]);
        return null;
    }

    /**
     * Get thumbnail path from original image path.
     *
     * @param string $imagePath
     * @return string
     */
    private function getThumbnailPathFromOriginal(string $imagePath): string
    {
        $pathInfo = pathinfo($imagePath);
        return $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
    }

}
