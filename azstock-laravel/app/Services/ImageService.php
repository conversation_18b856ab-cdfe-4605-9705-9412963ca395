<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageService
{
    private VercelBlobService $vercelBlobService;

    public function __construct(VercelBlobService $vercelBlobService)
    {
        $this->vercelBlobService = $vercelBlobService;
    }
{
    /**
     * Upload and process an image file.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param array $options
     * @return array
     */
    public function uploadImage(UploadedFile $file, string $directory, array $options = []): array
    {
        // Validate file
        $this->validateImage($file);

        // Generate unique filename with directory
        $filename = $this->vercelBlobService->generateFilename($file, $directory);

        // Upload to Vercel Blob Storage
        $uploadResult = $this->vercelBlobService->uploadFile($file, $filename);

        if (!$uploadResult['success']) {
            throw new \Exception('Failed to upload image to Vercel Blob Storage');
        }

        // Get file info
        $fileInfo = [
            'original_name' => $file->getClientOriginalName(),
            'filename' => basename($filename),
            'path' => $filename, // Store the full path for reference
            'url' => $uploadResult['url'],
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
        ];

        // Create thumbnail if requested
        if (isset($options['create_thumbnail']) && $options['create_thumbnail']) {
            $thumbnailInfo = $this->createThumbnail($file, $directory, $options);
            $fileInfo['thumbnail'] = $thumbnailInfo;
        }

        return $fileInfo;
    }
    
    /**
     * Upload multiple images.
     *
     * @param array $files
     * @param string $directory
     * @param array $options
     * @return array
     */
    public function uploadMultipleImages(array $files, string $directory, array $options = []): array
    {
        $uploadedFiles = [];
        $errors = [];
        
        foreach ($files as $index => $file) {
            try {
                $uploadedFiles[] = $this->uploadImage($file, $directory, $options);
            } catch (\Exception $e) {
                $errors[] = [
                    'index' => $index,
                    'filename' => $file->getClientOriginalName(),
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return [
            'uploaded' => $uploadedFiles,
            'errors' => $errors
        ];
    }
    
    /**
     * Delete an image file.
     *
     * @param string $urlOrPath
     * @return bool
     */
    public function deleteImage(string $urlOrPath): bool
    {
        // If it's a URL, use it directly; if it's a path, construct the URL
        $url = $this->isUrl($urlOrPath) ? $urlOrPath : $this->constructVercelUrl($urlOrPath);

        return $this->vercelBlobService->deleteFile($url);
    }

    /**
     * Check if a string is a URL
     *
     * @param string $string
     * @return bool
     */
    private function isUrl(string $string): bool
    {
        return filter_var($string, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Construct Vercel Blob URL from path
     *
     * @param string $path
     * @return string
     */
    private function constructVercelUrl(string $path): string
    {
        $baseUrl = config('services.vercel_blob.base_url');
        return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
    }
    
    /**
     * Delete multiple images.
     *
     * @param array $paths
     * @return array
     */
    public function deleteMultipleImages(array $paths): array
    {
        $deleted = [];
        $errors = [];
        
        foreach ($paths as $path) {
            try {
                if ($this->deleteImage($path)) {
                    $deleted[] = $path;
                } else {
                    $errors[] = $path;
                }
            } catch (\Exception $e) {
                $errors[] = $path;
            }
        }
        
        return [
            'deleted' => $deleted,
            'errors' => $errors
        ];
    }
    
    /**
     * Create a thumbnail for an image.
     *
     * @param UploadedFile $originalFile
     * @param string $directory
     * @param array $options
     * @return array
     */
    public function createThumbnail(UploadedFile $originalFile, string $directory, array $options = []): array
    {
        $width = $options['thumbnail_width'] ?? 300;
        $height = $options['thumbnail_height'] ?? 300;

        try {
            // Create thumbnail using intervention/image
            $manager = new ImageManager(new Driver());
            $image = $manager->read($originalFile->getContent());
            $image->cover($width, $height);

            // Generate thumbnail filename
            $originalName = pathinfo($originalFile->getClientOriginalName(), PATHINFO_FILENAME);
            $extension = $originalFile->getClientOriginalExtension();
            $thumbnailFilename = $directory . '/' . $originalName . '_thumb_' . time() . '.' . $extension;

            // Create a temporary file for the thumbnail
            $tempPath = tempnam(sys_get_temp_dir(), 'thumb_');
            $image->save($tempPath);

            // Create UploadedFile instance for thumbnail
            $thumbnailFile = new UploadedFile(
                $tempPath,
                $thumbnailFilename,
                $originalFile->getMimeType(),
                null,
                true
            );

            // Upload thumbnail to Vercel Blob
            $uploadResult = $this->vercelBlobService->uploadFile($thumbnailFile, $thumbnailFilename);

            // Clean up temp file
            unlink($tempPath);

            if ($uploadResult['success']) {
                return [
                    'path' => $thumbnailFilename,
                    'url' => $uploadResult['url'],
                    'width' => $width,
                    'height' => $height
                ];
            }
        } catch (\Exception $e) {
            // Log error but don't fail the main upload
            \Log::warning('Failed to create thumbnail', ['error' => $e->getMessage()]);
        }

        // Return null if thumbnail creation failed
        return [
            'path' => null,
            'url' => null,
            'width' => $width,
            'height' => $height
        ];
    }
    
    /**
     * Validate uploaded image file.
     *
     * @param UploadedFile $file
     * @throws \Exception
     */
    private function validateImage(UploadedFile $file): void
    {
        // Check if file is valid
        if (!$file->isValid()) {
            throw new \Exception('Invalid file upload');
        }
        
        // Check file size (max 5MB)
        $maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if ($file->getSize() > $maxSize) {
            throw new \Exception('File size exceeds maximum limit of 5MB');
        }
        
        // Check file type
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed');
        }
        
        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $allowedExtensions)) {
            throw new \Exception('Invalid file extension');
        }
    }
    
    /**
     * Generate a unique filename for the uploaded file.
     *
     * @param UploadedFile $file
     * @return string
     */
    private function generateFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        
        return $timestamp . '_' . $random . '.' . $extension;
    }
    
    /**
     * Get image URL from path.
     *
     * @param string $path
     * @return string
     */
    public function getImageUrl(string $path): string
    {
        // If it's already a URL, return as is
        if ($this->isUrl($path)) {
            return $path;
        }

        // Construct Vercel Blob URL
        return $this->constructVercelUrl($path);
    }

    /**
     * Check if image exists.
     * Note: For Vercel Blob, we'll assume images exist if we have a valid URL
     * since there's no direct "exists" check in the Vercel Blob API
     *
     * @param string $path
     * @return bool
     */
    public function imageExists(string $path): bool
    {
        // For Vercel Blob, we'll return true if we have a valid path/URL
        // In a production environment, you might want to make an HTTP HEAD request
        // to check if the file actually exists
        return !empty($path);
    }



    /**
     * Move images from temporary storage to product storage.
     * With Vercel Blob, images are already in cloud storage, so we just validate URLs.
     *
     * @param array $imageUrls
     * @param int $productId
     * @return array
     */
    public function moveImagesToProduct(array $imageUrls, int $productId): array
    {
        $movedImages = [];
        $errors = [];

        foreach ($imageUrls as $url) {
            try {
                // For Vercel Blob, we don't need to move files - they're already in the cloud
                // We just validate that the URL is accessible
                if (!$this->isUrl($url)) {
                    $errors[] = "Invalid URL format: {$url}";
                    continue;
                }

                // Check if it's a Vercel Blob URL
                $baseUrl = config('services.vercel_blob.base_url');
                if (strpos($url, $baseUrl) !== 0) {
                    $errors[] = "URL is not from Vercel Blob storage: {$url}";
                    continue;
                }

                // Extract filename for thumbnail URL generation
                $filename = basename(parse_url($url, PHP_URL_PATH));
                $thumbnailUrl = $this->generateThumbnailUrl($url);

                $movedImages[] = [
                    'old_path' => $url, // For Vercel Blob, path and URL are the same
                    'new_path' => $url,
                    'url' => $url,
                    'thumbnail_url' => $thumbnailUrl,
                ];

            } catch (\Exception $e) {
                $errors[] = "Error processing image {$url}: " . $e->getMessage();
            }
        }

        return [
            'moved' => $movedImages,
            'errors' => $errors
        ];
    }

    /**
     * Generate thumbnail URL from original image URL
     *
     * @param string $originalUrl
     * @return string|null
     */
    private function generateThumbnailUrl(string $originalUrl): ?string
    {
        // Extract path and generate thumbnail path
        $parsedUrl = parse_url($originalUrl);
        $path = $parsedUrl['path'] ?? '';

        $pathInfo = pathinfo($path);
        $thumbnailPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];

        return $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $thumbnailPath;
    }

}
