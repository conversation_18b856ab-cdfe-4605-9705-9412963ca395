<?php

namespace App\Services;

use App\Models\OtpCode;
use App\Models\User;
use App\Mail\OtpCodeMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Carbon\Carbon;

class OtpService
{
    /**
     * Send OTP code to user's email
     */
    public function sendOtp(
        string $email,
        string $purpose = 'login',
        int $expiryMinutes = 10,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): array {
        try {
            // Check rate limiting
            $rateLimitResult = OtpCode::canRequestOtp($email, $purpose);
            if (!$rateLimitResult['can_request']) {
                return [
                    'success' => false,
                    'message' => $rateLimitResult['message'],
                    'error_code' => 'RATE_LIMITED',
                    'wait_time' => $rateLimitResult['wait_time']
                ];
            }

            // Check if user exists (for login purpose)
            if ($purpose === 'login') {
                $user = User::where('email', $email)->first();
                if (!$user) {
                    return [
                        'success' => false,
                        'message' => 'No account found with this email address',
                        'error_code' => 'USER_NOT_FOUND'
                    ];
                }
            }

            // Generate OTP
            $otpData = OtpCode::createOtp(
                $email,
                $purpose,
                $expiryMinutes,
                $ipAddress,
                $userAgent
            );

            // Get user name for email
            $user = User::where('email', $email)->first();
            $userName = $user ? $user->name : '';

            // Send email
            Mail::to($email)->send(new OtpCodeMail(
                $otpData['code'],
                $userName,
                $purpose,
                $expiryMinutes
            ));

            Log::info('OTP sent successfully', [
                'email' => $email,
                'purpose' => $purpose,
                'otp_id' => $otpData['record']->id,
                'ip_address' => $ipAddress
            ]);

            return [
                'success' => true,
                'message' => 'OTP sent successfully',
                'expires_at' => $otpData['record']->expires_at->toISOString(),
                'expires_in_seconds' => $otpData['record']->getTimeRemaining()
            ];

        } catch (\Exception $e) {
            Log::error('Failed to send OTP', [
                'email' => $email,
                'purpose' => $purpose,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send OTP. Please try again.',
                'error_code' => 'SEND_FAILED'
            ];
        }
    }

    /**
     * Verify OTP code and authenticate user
     */
    public function verifyOtp(
        string $email,
        string $code,
        string $purpose = 'login'
    ): array {
        try {
            // Verify the OTP
            $verificationResult = OtpCode::verifyOtp($email, $code, $purpose);

            if (!$verificationResult['success']) {
                Log::warning('OTP verification failed', [
                    'email' => $email,
                    'purpose' => $purpose,
                    'error_code' => $verificationResult['error_code'],
                    'message' => $verificationResult['message']
                ]);

                return $verificationResult;
            }

            // For login purpose, authenticate the user
            if ($purpose === 'login') {
                $user = User::where('email', $email)->first();
                
                if (!$user) {
                    return [
                        'success' => false,
                        'message' => 'User not found',
                        'error_code' => 'USER_NOT_FOUND'
                    ];
                }

                // Generate authentication token
                $token = $user->createToken('otp_auth_token')->plainTextToken;

                Log::info('OTP authentication successful', [
                    'user_id' => $user->id,
                    'email' => $email,
                    'purpose' => $purpose
                ]);

                return [
                    'success' => true,
                    'message' => 'Authentication successful',
                    'user' => $user,
                    'token' => $token
                ];
            }

            // For other purposes, just return success
            Log::info('OTP verification successful', [
                'email' => $email,
                'purpose' => $purpose
            ]);

            return [
                'success' => true,
                'message' => 'OTP verified successfully'
            ];

        } catch (\Exception $e) {
            Log::error('OTP verification error', [
                'email' => $email,
                'purpose' => $purpose,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Verification failed. Please try again.',
                'error_code' => 'VERIFICATION_ERROR'
            ];
        }
    }

    /**
     * Clean up expired OTP codes
     */
    public function cleanupExpiredOtps(): int
    {
        return OtpCode::cleanupExpired();
    }

    /**
     * Get OTP statistics for monitoring
     */
    public function getOtpStats(string $email = null): array
    {
        $query = OtpCode::query();
        
        if ($email) {
            $query->where('email', $email);
        }

        $stats = [
            'total_generated' => $query->count(),
            'active_otps' => $query->active()->count(),
            'used_otps' => $query->where('is_used', true)->count(),
            'expired_otps' => $query->where('expires_at', '<', Carbon::now())->count(),
        ];

        if ($email) {
            $stats['recent_requests'] = $query->where('created_at', '>', Carbon::now()->subHour())->count();
        }

        return $stats;
    }

    /**
     * Revoke all active OTPs for a user
     */
    public function revokeUserOtps(string $email, string $purpose = null): int
    {
        $query = OtpCode::where('email', $email)
            ->where('is_used', false);

        if ($purpose) {
            $query->where('purpose', $purpose);
        }

        return $query->update(['is_used' => true]);
    }
}
