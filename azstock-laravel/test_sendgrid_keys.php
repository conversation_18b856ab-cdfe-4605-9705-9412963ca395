<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== SendGrid API Key Test ===\n\n";

$testEmail = '<EMAIL>';

// Test different API keys
$apiKeys = [
    'Original Key' => '*********************************************************************',
    'Updated Key' => '*********************************************************************',
    'Local Key' => '*********************************************************************',
];

foreach ($apiKeys as $keyName => $apiKey) {
    echo "Testing {$keyName}:\n";
    echo "Key: " . substr($apiKey, 0, 20) . "...\n";
    
    try {
        // Set the API key temporarily
        Config::set('mail.mailers.sendgrid.password', $apiKey);
        Config::set('mail.default', 'sendgrid');
        
        // Test sending a simple email
        Mail::raw("Test email from {$keyName} - " . date('Y-m-d H:i:s'), function ($message) use ($testEmail, $keyName) {
            $message->to($testEmail)
                    ->subject("SendGrid Test - {$keyName}")
                    ->from('<EMAIL>');
        });
        
        echo "✅ {$keyName} - Email sent successfully!\n";
        echo "Check your email inbox for the test message.\n\n";
        
    } catch (Exception $e) {
        echo "❌ {$keyName} - Failed: " . $e->getMessage() . "\n\n";
    }
    
    // Wait a bit between tests
    sleep(2);
}

echo "=== Test Complete ===\n";
echo "Check your email inbox to see which API key worked.\n";
echo "Update your .env file with the working API key.\n";
