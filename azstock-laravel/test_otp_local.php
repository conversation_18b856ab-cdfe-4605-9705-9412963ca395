<?php

// Set environment to testing
$_ENV['APP_ENV'] = 'testing';
putenv('APP_ENV=testing');

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Models\OtpCode;
use App\Models\User;
use App\Services\OtpService;
use Illuminate\Support\Facades\Config;

// Bootstrap Laravel with testing environment
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== OTP Local Test (Using SQLite) ===\n\n";

try {
    // Test database connection
    echo "1. Testing Database Connection:\n";
    $pdo = DB::connection()->getPdo();
    echo "✅ Database connected: " . DB::connection()->getDatabaseName() . "\n";
    
    // Check if tables exist
    $tables = DB::select("SELECT name FROM sqlite_master WHERE type='table'");
    $tableNames = array_column($tables, 'name');
    echo "Tables: " . implode(', ', $tableNames) . "\n\n";
    
    if (!in_array('otp_codes', $tableNames)) {
        echo "❌ otp_codes table not found. Run: php artisan migrate --env=testing\n\n";
        exit(1);
    }
    
    // Test user creation
    echo "2. Testing User Creation:\n";
    $testEmail = '<EMAIL>';
    $user = User::firstOrCreate(
        ['email' => $testEmail],
        [
            'name' => 'Test User',
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]
    );
    echo "✅ Test user: {$user->name} ({$user->email})\n\n";
    
    // Test OTP generation
    echo "3. Testing OTP Generation:\n";
    $otpData = OtpCode::createOtp($testEmail, 'login', 10);
    echo "✅ OTP generated: " . $otpData['code'] . "\n";
    echo "OTP ID: " . $otpData['record']->id . "\n";
    echo "Expires: " . $otpData['record']->expires_at . "\n\n";
    
    // Test OTP verification
    echo "4. Testing OTP Verification:\n";
    $verifyResult = OtpCode::verifyOtp($testEmail, $otpData['code'], 'login');
    echo "Verification: " . ($verifyResult['success'] ? '✅ SUCCESS' : '❌ FAILED') . "\n";
    echo "Message: " . $verifyResult['message'] . "\n\n";
    
    // Test email configuration (dry run)
    echo "5. Testing Email Configuration:\n";
    echo "Mail driver: " . config('mail.default') . "\n";
    echo "SendGrid API key: " . (config('mail.mailers.sendgrid.password') ? 'SET' : 'NOT SET') . "\n";
    echo "From address: " . config('mail.from.address') . "\n\n";
    
    // Test OTP Service
    echo "6. Testing OTP Service:\n";
    $otpService = new OtpService();
    
    // Set mail to log for testing
    Config::set('mail.default', 'log');
    
    $result = $otpService->sendOtp($testEmail, 'login', 10, '127.0.0.1', 'Local Test');
    echo "OTP Service: " . ($result['success'] ? '✅ SUCCESS' : '❌ FAILED') . "\n";
    echo "Message: " . $result['message'] . "\n";
    
    if ($result['success']) {
        echo "✅ Email logged to storage/logs/laravel.log\n";
    }
    
    echo "\n=== Test Complete ===\n";
    echo "To test real email sending, change MAIL_MAILER to 'sendgrid' in .env.testing\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
