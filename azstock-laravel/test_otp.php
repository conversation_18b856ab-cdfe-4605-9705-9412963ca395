<?php

// Simple test script to verify OTP functionality
require_once 'vendor/autoload.php';

use App\Models\OtpCode;
use App\Services\OtpService;

// Test OTP generation and verification
echo "Testing OTP System...\n\n";

// Test 1: Generate OTP
echo "1. Testing OTP Generation:\n";
$otpData = OtpCode::createOtp('<EMAIL>', 'login', 10);
echo "Generated OTP: " . $otpData['code'] . "\n";
echo "OTP ID: " . $otpData['record']->id . "\n";
echo "Expires at: " . $otpData['record']->expires_at . "\n\n";

// Test 2: Verify correct OTP
echo "2. Testing OTP Verification (correct code):\n";
$verifyResult = OtpCode::verifyOtp('<EMAIL>', $otpData['code'], 'login');
echo "Verification result: " . ($verifyResult['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $verifyResult['message'] . "\n\n";

// Test 3: Try to verify again (should fail - single use)
echo "3. Testing OTP Verification (already used):\n";
$verifyResult2 = OtpCode::verifyOtp('<EMAIL>', $otpData['code'], 'login');
echo "Verification result: " . ($verifyResult2['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $verifyResult2['message'] . "\n\n";

// Test 4: Test wrong code
echo "4. Testing OTP Verification (wrong code):\n";
$verifyResult3 = OtpCode::verifyOtp('<EMAIL>', '999999', 'login');
echo "Verification result: " . ($verifyResult3['success'] ? 'SUCCESS' : 'FAILED') . "\n";
echo "Message: " . $verifyResult3['message'] . "\n\n";

// Test 5: Rate limiting check
echo "5. Testing Rate Limiting:\n";
$rateLimitResult = OtpCode::canRequestOtp('<EMAIL>', 'login');
echo "Can request OTP: " . ($rateLimitResult['can_request'] ? 'YES' : 'NO') . "\n";
echo "Message: " . $rateLimitResult['message'] . "\n\n";

echo "OTP System Test Complete!\n";
