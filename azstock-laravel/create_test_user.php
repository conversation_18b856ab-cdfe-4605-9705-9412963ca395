<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Models\User;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Creating Test User ===\n\n";

$testEmail = '<EMAIL>'; // Change this to your real email for testing

try {
    // Check if user already exists
    $existingUser = User::where('email', $testEmail)->first();
    
    if ($existingUser) {
        echo "✅ Test user already exists:\n";
        echo "ID: {$existingUser->id}\n";
        echo "Name: {$existingUser->name}\n";
        echo "Email: {$existingUser->email}\n";
        echo "Created: {$existingUser->created_at}\n\n";
    } else {
        // Create new test user
        $user = User::create([
            'name' => 'Test User',
            'email' => $testEmail,
            'password' => bcrypt('password123'),
            'email_verified_at' => now(),
        ]);
        
        echo "✅ Test user created successfully:\n";
        echo "ID: {$user->id}\n";
        echo "Name: {$user->name}\n";
        echo "Email: {$user->email}\n";
        echo "Password: password123\n\n";
    }
    
    echo "Now you can test OTP with this email: {$testEmail}\n";
    echo "Test command:\n";
    echo "curl -X POST \"http://127.0.0.1:8000/api/otp/request\" -H \"Content-Type: application/json\" -H \"Accept: application/json\" -d '{\"email\":\"{$testEmail}\",\"purpose\":\"login\"}'\n\n";
    
} catch (Exception $e) {
    echo "❌ Error creating test user: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "=== Complete ===\n";
