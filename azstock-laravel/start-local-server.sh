#!/bin/bash

# Local Development Server Starter
# This script starts the Laravel server with local database configuration
# without modifying the main .env file

echo "🚀 Starting Laravel Local Development Server..."
echo "📍 URL: http://127.0.0.1:8000"
echo "💾 Using SQLite database for local development"
echo ""

# Create SQLite database if it doesn't exist
if [ ! -f "database/database.sqlite" ]; then
    echo "📁 Creating SQLite database..."
    touch database/database.sqlite
fi

# Check if migrations need to be run
echo "🔄 Checking database migrations..."
DB_CONNECTION=sqlite DB_DATABASE=/Users/<USER>/Desktop/az/azstock-laravel/database/database.sqlite /Users/<USER>/Library/Application\ Support/Herd/bin/php artisan migrate:status

echo ""
echo "🌟 Starting server with local configuration..."
echo "   - Database: SQLite (local)"
echo "   - Mail: SendGrid (configured)"
echo "   - Queue: Sync (immediate processing)"
echo ""
echo "Press Ctrl+C to stop the server"
echo "----------------------------------------"

# Start the server with environment overrides
DB_CONNECTION=sqlite \
DB_DATABASE=/Users/<USER>/Desktop/az/azstock-laravel/database/database.sqlite \
SESSION_DRIVER=file \
QUEUE_CONNECTION=sync \
/Users/<USER>/Library/Application\ Support/Herd/bin/php artisan serve --host=127.0.0.1 --port=8000
