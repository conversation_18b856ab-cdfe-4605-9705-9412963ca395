<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Mail;
use App\Mail\OtpCodeMail;
use App\Services\OtpService;
use App\Models\User;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== OTP vs Raw Email Delivery Test ===\n\n";

$testEmail = '<EMAIL>'; // Your email

echo "Testing email delivery to: {$testEmail}\n\n";

// Test 1: Raw Email (Your working method)
echo "1. TESTING RAW EMAIL (Your Working Method):\n";
try {
    Mail::raw('Test OTP email from raw method - ' . date('Y-m-d H:i:s'), function ($message) use ($testEmail) {
        $message->to($testEmail)
                ->subject('Test OTP - Raw Method')
                ->from('<EMAIL>');
    });
    echo "✅ Raw email sent successfully\n\n";
} catch (Exception $e) {
    echo "❌ Raw email failed: " . $e->getMessage() . "\n\n";
}

// Test 2: OTP Mailable (Direct)
echo "2. TESTING OTP MAILABLE (Direct Send):\n";
try {
    $testCode = '123456';
    $mail = new OtpCodeMail($testCode, 'Test User', 'login', 10);
    
    Mail::to($testEmail)->send($mail);
    echo "✅ OTP mailable sent successfully\n";
    echo "OTP Code: {$testCode}\n\n";
} catch (Exception $e) {
    echo "❌ OTP mailable failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n\n";
}

// Test 3: OTP Service (Full System)
echo "3. TESTING OTP SERVICE (Full System):\n";
try {
    // Ensure test user exists
    $user = User::firstOrCreate(
        ['email' => $testEmail],
        [
            'name' => 'Test User',
            'password' => bcrypt('password123'),
            'email_verified_at' => now(),
        ]
    );
    
    $otpService = new OtpService();
    $result = $otpService->sendOtp($testEmail, 'login', 10, '127.0.0.1', 'Test Script');
    
    if ($result['success']) {
        echo "✅ OTP Service sent successfully\n";
        echo "Message: " . $result['message'] . "\n";
        echo "Expires at: " . $result['expires_at'] . "\n\n";
    } else {
        echo "❌ OTP Service failed\n";
        echo "Message: " . $result['message'] . "\n";
        echo "Error code: " . ($result['error_code'] ?? 'N/A') . "\n\n";
    }
} catch (Exception $e) {
    echo "❌ OTP Service error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n\n";
}

// Test 4: Check Queue Status
echo "4. CHECKING QUEUE STATUS:\n";
echo "Queue Connection: " . config('queue.default') . "\n";
echo "Mail Driver: " . config('mail.default') . "\n";

try {
    // Check if there are any failed jobs
    $failedJobs = DB::table('failed_jobs')->count();
    echo "Failed Jobs: {$failedJobs}\n";
    
    // Check if there are any pending jobs
    if (config('queue.default') === 'database') {
        $pendingJobs = DB::table('jobs')->count();
        echo "Pending Jobs: {$pendingJobs}\n";
    }
} catch (Exception $e) {
    echo "Queue status check failed: " . $e->getMessage() . "\n";
}

echo "\n=== Test Results Summary ===\n";
echo "1. Raw Email: Should work (as you confirmed)\n";
echo "2. OTP Mailable: Should now work (removed queuing)\n";
echo "3. OTP Service: Should now work (uses OTP mailable)\n";
echo "4. Check your email inbox for all test emails\n\n";

echo "If OTP emails still don't arrive, check:\n";
echo "- Email templates exist and are valid\n";
echo "- No errors in Laravel logs\n";
echo "- SendGrid dashboard for delivery status\n";
echo "- Spam/junk folders\n\n";

echo "=== Test Complete ===\n";
