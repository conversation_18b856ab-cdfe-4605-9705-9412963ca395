<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Mail;
use App\Mail\OtpCodeMail;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== SendGrid Email Test ===\n\n";

// Check configuration
echo "1. CHECKING EMAIL CONFIGURATION:\n";
echo "Mail Driver: " . config('mail.default') . "\n";
echo "SendGrid Host: " . config('mail.mailers.sendgrid.host') . "\n";
echo "SendGrid Port: " . config('mail.mailers.sendgrid.port') . "\n";
echo "SendGrid Username: " . config('mail.mailers.sendgrid.username') . "\n";
echo "SendGrid API Key: " . (config('mail.mailers.sendgrid.password') ? 'SET (' . strlen(config('mail.mailers.sendgrid.password')) . ' chars)' : 'NOT SET') . "\n";
echo "From Address: " . config('mail.from.address') . "\n";
echo "From Name: " . config('mail.from.name') . "\n\n";

// Test email sending
echo "2. TESTING EMAIL SENDING:\n";
$testEmail = '<EMAIL>'; // Change this to your email for testing

try {
    $testCode = '123456';
    $mail = new OtpCodeMail($testCode, 'Test User', 'login', 10);
    
    echo "Sending test OTP email to: {$testEmail}\n";
    echo "OTP Code: {$testCode}\n";
    
    Mail::to($testEmail)->send($mail);
    
    echo "✅ Email sent successfully!\n";
    echo "Check your email inbox (and spam folder) for the OTP email.\n\n";
    
} catch (Exception $e) {
    echo "❌ Email sending failed!\n";
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n\n";
}

echo "3. DEBUGGING TIPS:\n";
echo "- Check your SendGrid account dashboard for delivery logs\n";
echo "- Verify your SendGrid API key is active and has sending permissions\n";
echo "- Check if your sender email domain is verified in SendGrid\n";
echo "- Look for emails in spam/junk folder\n";
echo "- Check Laravel logs: storage/logs/laravel.log\n\n";

echo "=== Test Complete ===\n";
