<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove the direct relationship between auction and auction_item
        // Now auction_items will reference auctions instead
        Schema::table('auctions', function (Blueprint $table) {
            $table->dropForeign(['auction_item_id']);
            $table->dropColumn('auction_item_id');
        });

        // Add auction_id to auction_items table to create proper relationship
        Schema::table('auction_items', function (Blueprint $table) {
            $table->unsignedBigInteger('auction_id')->nullable()->after('vendor_id');
            $table->foreign('auction_id')->references('id')->on('auctions')->onDelete('set null');
            $table->index('auction_id');
        });

        // Add additional fields to auction_items for better auction management
        Schema::table('auction_items', function (Blueprint $table) {
            $table->decimal('starting_price', 10, 2)->nullable()->after('image');
            $table->decimal('reserve_price', 10, 2)->nullable()->after('starting_price');
            $table->integer('quantity')->default(1)->after('reserve_price');
            $table->string('condition')->nullable()->after('quantity');
            $table->text('item_notes')->nullable()->after('condition');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove new columns from auction_items
        Schema::table('auction_items', function (Blueprint $table) {
            $table->dropForeign(['auction_id']);
            $table->dropIndex(['auction_id']);
            $table->dropColumn([
                'auction_id', 
                'starting_price', 
                'reserve_price', 
                'quantity', 
                'condition', 
                'item_notes'
            ]);
        });

        // Restore the old relationship structure
        Schema::table('auctions', function (Blueprint $table) {
            $table->unsignedBigInteger('auction_item_id')->nullable()->after('vendor_id');
            $table->foreign('auction_item_id')->references('id')->on('auction_items')->onDelete('cascade');
        });
    }
};
