<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, drop foreign key constraints that reference products table
        Schema::table('auctions', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
        });

        Schema::table('product_images', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
        });

        Schema::table('reviews', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
        });

        // Rename the products table to auction_items
        Schema::rename('products', 'auction_items');

        // Rename product_images table to auction_item_images
        Schema::rename('product_images', 'auction_item_images');

        // Update auction_item_images table column name
        Schema::table('auction_item_images', function (Blueprint $table) {
            $table->renameColumn('product_id', 'auction_item_id');
        });

        // Update reviews table column name
        Schema::table('reviews', function (Blueprint $table) {
            $table->renameColumn('product_id', 'auction_item_id');
        });

        // Update auctions table to reference auction_items
        Schema::table('auctions', function (Blueprint $table) {
            $table->renameColumn('product_id', 'auction_item_id');
        });

        // Re-add foreign key constraints with new names
        Schema::table('auctions', function (Blueprint $table) {
            $table->foreign('auction_item_id')->references('id')->on('auction_items')->onDelete('cascade');
        });

        Schema::table('auction_item_images', function (Blueprint $table) {
            $table->foreign('auction_item_id')->references('id')->on('auction_items')->onDelete('cascade');
        });

        Schema::table('reviews', function (Blueprint $table) {
            $table->foreign('auction_item_id')->references('id')->on('auction_items')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints
        Schema::table('auctions', function (Blueprint $table) {
            $table->dropForeign(['auction_item_id']);
        });

        Schema::table('auction_item_images', function (Blueprint $table) {
            $table->dropForeign(['auction_item_id']);
        });

        Schema::table('reviews', function (Blueprint $table) {
            $table->dropForeign(['auction_item_id']);
        });

        // Rename columns back
        Schema::table('auctions', function (Blueprint $table) {
            $table->renameColumn('auction_item_id', 'product_id');
        });

        Schema::table('auction_item_images', function (Blueprint $table) {
            $table->renameColumn('auction_item_id', 'product_id');
        });

        Schema::table('reviews', function (Blueprint $table) {
            $table->renameColumn('auction_item_id', 'product_id');
        });

        // Rename tables back
        Schema::rename('auction_item_images', 'product_images');
        Schema::rename('auction_items', 'products');

        // Re-add original foreign key constraints
        Schema::table('auctions', function (Blueprint $table) {
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });

        Schema::table('product_images', function (Blueprint $table) {
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });

        Schema::table('reviews', function (Blueprint $table) {
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });
    }
};
