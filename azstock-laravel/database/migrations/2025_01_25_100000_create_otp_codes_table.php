<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('otp_codes', function (Blueprint $table) {
            $table->id();
            $table->string('email')->index();
            $table->string('code_hash'); // Store hashed OTP for security
            $table->string('purpose')->default('login'); // login, password_reset, etc.
            $table->timestamp('expires_at');
            $table->boolean('is_used')->default(false);
            $table->string('ip_address')->nullable();
            $table->string('user_agent')->nullable();
            $table->integer('attempts')->default(0); // Track verification attempts
            $table->timestamps();

            // Indexes for performance
            $table->index(['email', 'purpose']);
            $table->index(['expires_at', 'is_used']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('otp_codes');
    }
};
