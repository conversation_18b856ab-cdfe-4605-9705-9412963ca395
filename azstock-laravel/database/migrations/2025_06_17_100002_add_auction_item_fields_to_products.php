<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // Add auction-related fields to existing products table
            $table->unsignedBigInteger('auction_id')->nullable()->after('vendor_id');
            $table->decimal('starting_price', 10, 2)->nullable()->after('image');
            $table->decimal('current_price', 10, 2)->nullable()->after('starting_price');
            $table->decimal('reserve_price', 10, 2)->nullable()->after('current_price');
            $table->integer('quantity')->default(1)->after('reserve_price');
            $table->string('condition')->nullable()->after('quantity');
            $table->text('item_notes')->nullable()->after('condition');
            
            // Add foreign key and indexes
            $table->foreign('auction_id')->references('id')->on('auctions')->onDelete('set null');
            $table->index('auction_id');
            $table->index('condition');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['auction_id']);
            $table->dropIndex(['auction_id']);
            $table->dropIndex(['condition']);
            $table->dropColumn([
                'auction_id',
                'starting_price',
                'current_price', 
                'reserve_price',
                'quantity',
                'condition',
                'item_notes'
            ]);
        });
    }
};
