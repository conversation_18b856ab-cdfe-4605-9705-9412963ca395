<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('auctions', function (Blueprint $table) {
            // Add new columns
            $table->text('description')->nullable()->after('current_price');
            $table->enum('auction_type', ['online', 'sealed'])->default('online')->after('description');
            $table->string('featured_image')->nullable()->after('auction_type');
            
            // Add indexes for better performance
            $table->index('auction_type');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('auctions', function (Blueprint $table) {
            $table->dropIndex(['auction_type']);
            $table->dropIndex(['status']);
            $table->dropColumn(['description', 'auction_type', 'featured_image']);
        });
    }
};
