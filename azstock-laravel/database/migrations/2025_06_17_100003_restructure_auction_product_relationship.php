<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, let's add auction_id to products table to support multiple products per auction
        Schema::table('products', function (Blueprint $table) {
            $table->unsignedBigInteger('auction_id')->nullable()->after('vendor_id');
            $table->foreign('auction_id')->references('id')->on('auctions')->onDelete('set null');
            $table->index('auction_id');
        });

        // Migrate existing data: for each auction, set the auction_id on the related product
        $auctions = DB::table('auctions')->get();
        foreach ($auctions as $auction) {
            DB::table('products')
                ->where('id', $auction->product_id)
                ->update(['auction_id' => $auction->id]);
        }

        // Now remove the product_id from auctions table since products will reference auctions
        Schema::table('auctions', function (Blueprint $table) {
            $table->dropForeign(['product_id']);
            $table->dropColumn('product_id');
        });

        // Move auction-specific pricing from auctions to products
        Schema::table('products', function (Blueprint $table) {
            $table->decimal('starting_price', 10, 2)->nullable()->after('image');
            $table->decimal('current_price', 10, 2)->nullable()->after('starting_price');
            $table->decimal('reserve_price', 10, 2)->nullable()->after('current_price');
            $table->integer('quantity')->default(1)->after('reserve_price');
            $table->string('condition')->nullable()->after('quantity');
        });

        // Migrate pricing data from auctions to products
        $auctions = DB::table('auctions')->get();
        foreach ($auctions as $auction) {
            DB::table('products')
                ->where('auction_id', $auction->id)
                ->update([
                    'starting_price' => $auction->starting_price,
                    'current_price' => $auction->current_price,
                    'reserve_price' => $auction->reserve_price,
                ]);
        }

        // Remove pricing columns from auctions table
        Schema::table('auctions', function (Blueprint $table) {
            $table->dropColumn(['starting_price', 'current_price', 'reserve_price']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore pricing columns to auctions table
        Schema::table('auctions', function (Blueprint $table) {
            $table->decimal('starting_price', 10, 2)->after('end_time');
            $table->decimal('current_price', 10, 2)->nullable()->after('starting_price');
            $table->decimal('reserve_price', 10, 2)->nullable()->after('current_price');
        });

        // Migrate pricing data back to auctions
        $products = DB::table('products')->whereNotNull('auction_id')->get();
        foreach ($products as $product) {
            DB::table('auctions')
                ->where('id', $product->auction_id)
                ->update([
                    'starting_price' => $product->starting_price,
                    'current_price' => $product->current_price,
                    'reserve_price' => $product->reserve_price,
                ]);
        }

        // Add product_id back to auctions table
        Schema::table('auctions', function (Blueprint $table) {
            $table->unsignedBigInteger('product_id')->nullable()->after('vendor_id');
        });

        // Migrate the first product of each auction back to auction.product_id
        $auctions = DB::table('auctions')->get();
        foreach ($auctions as $auction) {
            $firstProduct = DB::table('products')->where('auction_id', $auction->id)->first();
            if ($firstProduct) {
                DB::table('auctions')
                    ->where('id', $auction->id)
                    ->update(['product_id' => $firstProduct->id]);
            }
        }

        // Add foreign key constraint back
        Schema::table('auctions', function (Blueprint $table) {
            $table->foreign('product_id')->references('id')->on('products')->onDelete('cascade');
        });

        // Remove auction-related columns from products
        Schema::table('products', function (Blueprint $table) {
            $table->dropForeign(['auction_id']);
            $table->dropIndex(['auction_id']);
            $table->dropColumn([
                'auction_id', 
                'starting_price', 
                'current_price', 
                'reserve_price', 
                'quantity', 
                'condition'
            ]);
        });
    }
};
