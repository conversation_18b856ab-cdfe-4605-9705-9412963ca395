#!/bin/bash

# Laravel Local Development Server
# Uses .env.local.dev for local database configuration

echo "🚀 Starting Laravel Local Development Server..."
echo "📍 URL: http://127.0.0.1:8000"
echo "💾 Using local SQLite database"
echo "📧 Using SendGrid for email (configured)"
echo ""

# Backup current .env if it exists
if [ -f ".env" ]; then
    echo "📋 Backing up current .env to .env.backup"
    cp .env .env.backup
fi

# Copy local development config
echo "⚙️  Setting up local development configuration..."
cp .env.local.dev .env

# Create SQLite database if it doesn't exist
if [ ! -f "database/database.sqlite" ]; then
    echo "📁 Creating SQLite database..."
    touch database/database.sqlite
fi

# Run migrations
echo "🔄 Running database migrations..."
/Users/<USER>/Library/Application\ Support/Herd/bin/php artisan migrate --force

echo ""
echo "🌟 Server starting with local configuration:"
echo "   - Database: SQLite (database/database.sqlite)"
echo "   - Mail: SendGrid"
echo "   - Queue: Sync (immediate processing)"
echo "   - Session: File-based"
echo ""
echo "📝 Test URLs:"
echo "   - OTP Debug: http://127.0.0.1:8000/test-otp-debug.html"
echo "   - API Status: http://127.0.0.1:8000/api/otp/debug/status"
echo ""
echo "Press Ctrl+C to stop the server"
echo "----------------------------------------"

# Function to restore .env on exit
cleanup() {
    echo ""
    echo "🔄 Restoring original .env file..."
    if [ -f ".env.backup" ]; then
        mv .env.backup .env
        echo "✅ Original .env restored"
    fi
    echo "👋 Server stopped"
    exit 0
}

# Set trap to restore .env on script exit
trap cleanup INT TERM EXIT

# Start the server
/Users/<USER>/Library/Application\ Support/Herd/bin/php artisan serve --host=127.0.0.1 --port=8000
