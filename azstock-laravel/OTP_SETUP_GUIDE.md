# OTP Authentication Setup Guide

## Overview
This guide will help you set up the OTP (One-Time Password) authentication system for the AZ Stock application.

## Backend Setup (Laravel)

### 1. Environment Configuration

Add the following to your `.env` file:

```env
# SendGrid Configuration
MAIL_MAILER=sendgrid
SENDGRID_API_KEY=*********************************************************************

# Mail Configuration
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="AZ Stock"

# Queue Configuration (recommended for email sending)
QUEUE_CONNECTION=database

# OTP Configuration (optional - defaults are set in code)
OTP_EXPIRY_MINUTES=10
OTP_MAX_ATTEMPTS=5
OTP_RATE_LIMIT_PER_MINUTE=3
OTP_RATE_LIMIT_PER_HOUR=10
```

### 2. Database Migration

Run the migration to create the OTP codes table:

```bash
php artisan migrate
```

### 3. Queue Setup (Recommended)

For better email delivery performance, set up queues:

```bash
# Create jobs table if not exists
php artisan queue:table
php artisan migrate

# Run queue worker
php artisan queue:work
```

### 4. Cleanup Command

Set up a cron job to clean expired OTP codes:

```bash
# Add to your crontab
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

Add to `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    $schedule->command('otp:cleanup')->hourly();
}
```

## Frontend Setup (Next.js)

### 1. Environment Configuration

Update your `.env.development` file:

```env
NEXT_PUBLIC_API_URL=https://azstock-laravel-production.up.railway.app/api
NEXT_PUBLIC_AUTH_URL=https://azstock-laravel-production.up.railway.app/api
```

### 2. Dependencies

The OTP system uses existing dependencies. No additional packages needed.

## API Endpoints

### Public Endpoints

#### Request OTP
```
POST /api/otp/request
Content-Type: application/json

{
  "email": "<EMAIL>",
  "purpose": "login"
}
```

#### Verify OTP
```
POST /api/otp/verify
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "purpose": "login"
}
```

#### Resend OTP
```
POST /api/otp/resend
Content-Type: application/json

{
  "email": "<EMAIL>",
  "purpose": "login"
}
```

### Admin Endpoints (Authenticated)

#### Get OTP Status
```
GET /api/admin/otp/status?email=<EMAIL>
Authorization: Bearer {token}
```

#### Revoke User OTPs
```
POST /api/admin/otp/revoke
Authorization: Bearer {token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "purpose": "login"
}
```

## Usage Flow

### 1. User Login with OTP

1. User visits `/login-otp`
2. User enters email address
3. System sends OTP to email
4. User enters 6-digit OTP code
5. System verifies OTP and authenticates user
6. User is redirected to dashboard

### 2. Integration with Existing Login

- Regular login page now includes "Sign in with OTP" link
- Users can choose between password or OTP authentication
- Both methods use the same NextAuth session management

## Security Features

### Rate Limiting
- 3 OTP requests per minute per email
- 10 OTP requests per hour per email
- 5 OTP requests per minute per IP
- 10 verification attempts per 5 minutes per email

### OTP Security
- 6-digit numeric codes
- 10-minute expiration
- Single-use only
- Hashed storage in database
- Maximum 5 verification attempts per OTP

### Email Security
- Professional email templates
- Security warnings included
- Clear expiration information
- Branded with AZ Stock identity

## Testing

### Manual Testing

1. **Test OTP Request:**
   ```bash
   curl -X POST "https://azstock-laravel-production.up.railway.app/api/otp/request" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","purpose":"login"}'
   ```

2. **Test OTP Verification:**
   ```bash
   curl -X POST "https://azstock-laravel-production.up.railway.app/api/otp/verify" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","code":"123456","purpose":"login"}'
   ```

### Frontend Testing

1. Visit `http://localhost:3000/login-otp`
2. Enter a valid email address
3. Check email for OTP code
4. Enter the 6-digit code
5. Verify successful authentication

## Troubleshooting

### Common Issues

1. **Emails not sending:**
   - Check SendGrid API key
   - Verify MAIL_FROM_ADDRESS is configured
   - Check Laravel logs for email errors

2. **OTP verification fails:**
   - Check if OTP has expired (10 minutes)
   - Verify correct email and code
   - Check rate limiting

3. **Frontend authentication fails:**
   - Verify API URLs in environment
   - Check NextAuth configuration
   - Review browser console for errors

### Debug Commands

```bash
# Check OTP status
php artisan tinker
>>> App\Models\OtpCode::where('email', '<EMAIL>')->get();

# Clean expired OTPs
php artisan otp:cleanup

# Check mail configuration
php artisan tinker
>>> config('mail');
```

## Production Considerations

1. **Email Deliverability:**
   - Configure SPF, DKIM, and DMARC records
   - Use a verified sender domain
   - Monitor SendGrid delivery statistics

2. **Rate Limiting:**
   - Adjust rate limits based on usage patterns
   - Consider implementing CAPTCHA for high-risk scenarios

3. **Monitoring:**
   - Set up alerts for failed OTP deliveries
   - Monitor OTP usage patterns
   - Track authentication success rates

4. **Backup Authentication:**
   - Keep password authentication available
   - Implement account recovery mechanisms
   - Provide admin override capabilities

## Support

For issues or questions:
1. Check Laravel logs: `storage/logs/laravel.log`
2. Check queue logs if using queues
3. Review SendGrid delivery logs
4. Test API endpoints directly with curl/Postman
