<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use App\Models\OtpCode;
use App\Models\User;
use App\Services\OtpService;
use App\Mail\OtpCodeMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== OTP System Debug Script ===\n\n";

// Test 1: Check Environment Configuration
echo "1. CHECKING ENVIRONMENT CONFIGURATION:\n";
echo "APP_KEY: " . (config('app.key') ? 'SET' : 'NOT SET') . "\n";
echo "MAIL_MAILER: " . config('mail.default') . "\n";
echo "SENDGRID_API_KEY: " . (config('mail.mailers.sendgrid.password') ? 'SET' : 'NOT SET') . "\n";
echo "MAIL_FROM_ADDRESS: " . config('mail.from.address') . "\n";
echo "MAIL_FROM_NAME: " . config('mail.from.name') . "\n";
echo "DATABASE_CONNECTION: " . config('database.default') . "\n";
echo "\n";

// Test 2: Check Database Connection
echo "2. CHECKING DATABASE CONNECTION:\n";
try {
    $pdo = DB::connection()->getPdo();
    echo "✅ Database connection successful\n";
    
    // Check if otp_codes table exists
    $tables = DB::select("SHOW TABLES LIKE 'otp_codes'");
    if (count($tables) > 0) {
        echo "✅ otp_codes table exists\n";
        
        // Check table structure
        $columns = DB::select("DESCRIBE otp_codes");
        echo "Table columns: " . implode(', ', array_column($columns, 'Field')) . "\n";
    } else {
        echo "❌ otp_codes table does not exist\n";
    }
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 3: Check if User exists for testing
echo "3. CHECKING TEST USER:\n";
$testEmail = '<EMAIL>';
try {
    $user = User::where('email', $testEmail)->first();
    if ($user) {
        echo "✅ Test user exists: {$user->name} ({$user->email})\n";
    } else {
        echo "⚠️  Test user does not exist, creating one...\n";
        $user = User::create([
            'name' => 'Test User',
            'email' => $testEmail,
            'password' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);
        echo "✅ Test user created: {$user->name} ({$user->email})\n";
    }
} catch (Exception $e) {
    echo "❌ User check/creation failed: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 4: Test OTP Generation
echo "4. TESTING OTP GENERATION:\n";
try {
    $otpData = OtpCode::createOtp($testEmail, 'login', 10);
    echo "✅ OTP generated successfully\n";
    echo "OTP Code: " . $otpData['code'] . "\n";
    echo "OTP ID: " . $otpData['record']->id . "\n";
    echo "Expires at: " . $otpData['record']->expires_at . "\n";
    echo "Code hash: " . substr($otpData['record']->code_hash, 0, 20) . "...\n";
} catch (Exception $e) {
    echo "❌ OTP generation failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
echo "\n";

// Test 5: Test Email Configuration
echo "5. TESTING EMAIL CONFIGURATION:\n";
try {
    $mailConfig = config('mail');
    echo "Mail driver: " . $mailConfig['default'] . "\n";
    
    $sendgridConfig = $mailConfig['mailers']['sendgrid'] ?? null;
    if ($sendgridConfig) {
        echo "SendGrid host: " . $sendgridConfig['host'] . "\n";
        echo "SendGrid port: " . $sendgridConfig['port'] . "\n";
        echo "SendGrid username: " . $sendgridConfig['username'] . "\n";
        echo "SendGrid password: " . (strlen($sendgridConfig['password']) > 0 ? 'SET (' . strlen($sendgridConfig['password']) . ' chars)' : 'NOT SET') . "\n";
    } else {
        echo "❌ SendGrid configuration not found\n";
    }
} catch (Exception $e) {
    echo "❌ Email configuration check failed: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 6: Test Email Template
echo "6. TESTING EMAIL TEMPLATE:\n";
try {
    $testCode = '123456';
    $mail = new OtpCodeMail($testCode, 'Test User', 'login', 10);
    echo "✅ OtpCodeMail instance created successfully\n";
    echo "Subject: " . $mail->envelope()->subject . "\n";
    echo "Template: " . $mail->content()->html . "\n";
} catch (Exception $e) {
    echo "❌ Email template test failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
echo "\n";

// Test 7: Test Email Sending (Dry Run)
echo "7. TESTING EMAIL SENDING (DRY RUN):\n";
try {
    // Set mail driver to log for testing
    Config::set('mail.default', 'log');
    
    $testCode = '123456';
    $mail = new OtpCodeMail($testCode, 'Test User', 'login', 10);
    
    Mail::to($testEmail)->send($mail);
    echo "✅ Email sent successfully (logged to storage/logs/laravel.log)\n";
    
    // Reset to original mail driver
    Config::set('mail.default', 'sendgrid');
} catch (Exception $e) {
    echo "❌ Email sending failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
echo "\n";

// Test 8: Test OTP Service
echo "8. TESTING OTP SERVICE:\n";
try {
    $otpService = new OtpService();
    
    // Test rate limiting check
    $rateLimitResult = OtpCode::canRequestOtp($testEmail, 'login');
    echo "Rate limit check: " . ($rateLimitResult['can_request'] ? 'ALLOWED' : 'BLOCKED') . "\n";
    echo "Rate limit message: " . $rateLimitResult['message'] . "\n";
    
    if ($rateLimitResult['can_request']) {
        // Test OTP sending (with log driver)
        Config::set('mail.default', 'log');
        $result = $otpService->sendOtp($testEmail, 'login', 10, '127.0.0.1', 'Debug Script');
        echo "OTP Service result: " . ($result['success'] ? 'SUCCESS' : 'FAILED') . "\n";
        echo "OTP Service message: " . $result['message'] . "\n";
        
        if (isset($result['expires_at'])) {
            echo "Expires at: " . $result['expires_at'] . "\n";
        }
        
        Config::set('mail.default', 'sendgrid');
    }
} catch (Exception $e) {
    echo "❌ OTP Service test failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
echo "\n";

// Test 9: Check Recent OTP Records
echo "9. CHECKING RECENT OTP RECORDS:\n";
try {
    $recentOtps = OtpCode::where('email', $testEmail)
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
    
    echo "Found " . $recentOtps->count() . " recent OTP records for {$testEmail}:\n";
    foreach ($recentOtps as $otp) {
        echo "- ID: {$otp->id}, Purpose: {$otp->purpose}, Used: " . ($otp->is_used ? 'YES' : 'NO') . 
             ", Expires: {$otp->expires_at}, Created: {$otp->created_at}\n";
    }
} catch (Exception $e) {
    echo "❌ OTP records check failed: " . $e->getMessage() . "\n";
}
echo "\n";

echo "=== DEBUG COMPLETE ===\n";
echo "Check storage/logs/laravel.log for detailed logs\n";
