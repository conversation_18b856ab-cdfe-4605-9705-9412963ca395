<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OTP System Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>OTP System Debug Test</h1>
    <p>This page will test the OTP system on your deployed Railway application.</p>

    <div class="test-section info">
        <h3>Configuration</h3>
        <label>API Base URL:</label>
        <input type="text" id="apiUrl" value="https://azstock-laravel-production.up.railway.app/api" style="width: 400px;">
        <br>
        <label>Test Email:</label>
        <input type="email" id="testEmail" value="<EMAIL>" style="width: 300px;">
    </div>

    <div class="test-section">
        <h3>Test 1: Request OTP</h3>
        <button onclick="testRequestOtp()">Send OTP Request</button>
        <div id="otpRequestResult"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Verify OTP</h3>
        <input type="text" id="otpCode" placeholder="Enter 6-digit OTP code" maxlength="6">
        <button onclick="testVerifyOtp()">Verify OTP</button>
        <div id="otpVerifyResult"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Check Database Connection</h3>
        <button onclick="testDatabaseConnection()">Test Database</button>
        <div id="dbTestResult"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Check Email Configuration</h3>
        <button onclick="testEmailConfig()">Test Email Config</button>
        <div id="emailTestResult"></div>
    </div>

    <div class="test-section">
        <h3>Debug Logs</h3>
        <div id="debugLogs"></div>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('debugLogs');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logEntry.className = type;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('debugLogs').innerHTML = '';
        }

        async function makeRequest(endpoint, method = 'GET', body = null) {
            const apiUrl = document.getElementById('apiUrl').value;
            const url = `${apiUrl}${endpoint}`;
            
            log(`Making ${method} request to: ${url}`);
            
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
            };

            if (body) {
                options.body = JSON.stringify(body);
                log(`Request body: ${JSON.stringify(body, null, 2)}`);
            }

            try {
                const response = await fetch(url, options);
                const data = await response.json();
                
                log(`Response status: ${response.status}`);
                log(`Response data: ${JSON.stringify(data, null, 2)}`);
                
                return { response, data };
            } catch (error) {
                log(`Request failed: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testRequestOtp() {
            const resultDiv = document.getElementById('otpRequestResult');
            const email = document.getElementById('testEmail').value;
            
            if (!email) {
                resultDiv.innerHTML = '<div class="error">Please enter a test email address</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">Sending OTP request...</div>';
                
                const { response, data } = await makeRequest('/otp/request', 'POST', {
                    email: email,
                    purpose: 'login'
                });

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ OTP Request Successful</h4>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <p><strong>Expires At:</strong> ${data.expires_at || 'Not provided'}</p>
                            <p><strong>Expires In:</strong> ${data.expires_in_seconds || 'Not provided'} seconds</p>
                            <p>Check your email for the OTP code!</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ OTP Request Failed</h4>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <p><strong>Error Code:</strong> ${data.error_code || 'Not provided'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Request Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testVerifyOtp() {
            const resultDiv = document.getElementById('otpVerifyResult');
            const email = document.getElementById('testEmail').value;
            const code = document.getElementById('otpCode').value;
            
            if (!email || !code) {
                resultDiv.innerHTML = '<div class="error">Please enter both email and OTP code</div>';
                return;
            }

            if (code.length !== 6) {
                resultDiv.innerHTML = '<div class="error">OTP code must be 6 digits</div>';
                return;
            }

            try {
                resultDiv.innerHTML = '<div class="info">Verifying OTP...</div>';
                
                const { response, data } = await makeRequest('/otp/verify', 'POST', {
                    email: email,
                    code: code,
                    purpose: 'login'
                });

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ OTP Verification Successful</h4>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <p><strong>Token:</strong> ${data.token ? 'Provided' : 'Not provided'}</p>
                            <p><strong>User Data:</strong> ${data.data ? JSON.stringify(data.data, null, 2) : 'Not provided'}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ OTP Verification Failed</h4>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <p><strong>Error Code:</strong> ${data.error_code || 'Not provided'}</p>
                            <p><strong>Attempts Remaining:</strong> ${data.attempts_remaining || 'Not provided'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Request Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testDatabaseConnection() {
            const resultDiv = document.getElementById('dbTestResult');
            
            try {
                resultDiv.innerHTML = '<div class="info">Testing database connection...</div>';
                
                // Test by trying to request an OTP (this will test DB connection)
                const { response, data } = await makeRequest('/otp/request', 'POST', {
                    email: '<EMAIL>',
                    purpose: 'login'
                });

                if (response.status !== 500) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Database Connection Working</h4>
                            <p>The database is accessible and OTP table exists.</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Database Connection Issue</h4>
                            <p>Response: ${JSON.stringify(data, null, 2)}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Database Test Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testEmailConfig() {
            const resultDiv = document.getElementById('emailTestResult');
            
            try {
                resultDiv.innerHTML = '<div class="info">Testing email configuration...</div>';
                
                // Test by requesting an OTP to a test email
                const { response, data } = await makeRequest('/otp/request', 'POST', {
                    email: '<EMAIL>',
                    purpose: 'login'
                });

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Email Configuration Working</h4>
                            <p>OTP was generated and email sending was initiated successfully.</p>
                            <p><strong>Message:</strong> ${data.message}</p>
                        </div>
                    `;
                } else if (data.error_code === 'USER_NOT_FOUND') {
                    resultDiv.innerHTML = `
                        <div class="info">
                            <h4>ℹ️ Email Config Test Result</h4>
                            <p>Email configuration appears to be working, but user doesn't exist.</p>
                            <p>This is expected for a test email address.</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Email Configuration Issue</h4>
                            <p><strong>Message:</strong> ${data.message}</p>
                            <p><strong>Error Code:</strong> ${data.error_code}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Email Test Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Initialize
        log('OTP Debug Test Page Loaded');
        log('Ready to test OTP system on Railway deployment');
    </script>
</body>
</html>
